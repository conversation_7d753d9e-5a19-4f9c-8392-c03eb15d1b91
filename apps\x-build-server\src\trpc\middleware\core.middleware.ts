import { ConsoleLogger, Inject, Injectable } from '@nestjs/common';
// eslint-disable-next-line prettier/prettier
import { MiddlewareOptions, MiddlewareResponse, TRPCMiddleware } from 'nestjs-trpc';

@Injectable()
export class CoreMiddleware implements TRPCMiddleware {
  // constructror(

  // ) { }

  async use(opts: MiddlewareOptions<object>): Promise<MiddlewareResponse> {
    const start = Date.now();
    const result: any = await opts.next({
      ctx: {
        ben: 1,
      },
    });

    const durationMs = Date.now() - start;
    const meta = { path: opts.path, type: opts.type, durationMs };

    result.ok ? console.log('OK request timing:', meta) : console.error('Non-OK request timing', meta);
    return result;
  }
}

import type { ScatterChartProps } from '@web/src/types'
import { forwardRef } from 'react'
import { keepFixFloat, mapMetricToText, resolveField } from '@web/src/utils'
import EChart from './EChart'
import { isEmpty } from 'radash'

const metricRules = [
    ['total', 'rebase'],
    ['total', 'increase_rate'],
    ['total', 'tgi'],
    ['match', 'tgi'],
    ['match', 'increase_rate'],
    ['match', 'rebase'],
    ['rebase', 'increase_rate'],
    ['rebase', 'tgi'],
    ['increase_rate', 'tgi'],
]
function ScatterChart({ data, tags, metrics, tagDims, period, chartId, path, setPath, drillDown }: ScatterChartProps, ref: any) {
    if (period && period.length > 0) {
        return <div>散点图无时间字段</div>
    }
    if (metrics.length !== 2) {
        return <div>请选择两个指标</div>
    }
    if (!metricRules.some(rule => rule.join() === metrics.join())) {
        return <div>请选择合适的两个指标</div>
    }
    if (!data.length || isEmpty(data[0])) {
        return <div>暂无数据</div>
    }
    const onclick = (params: any) => {
        if (chartId && drillDown) {
            drillDown(chartId, params.value[2], tagDims)
        }
        if (path && setPath) {
            setPath([...path, params.value[2]])
        }
    }
    const options = {
        // title: {
        //     text: name || '',
        // },
        xAxis: {
            name: metrics[0],
            axisLabel: {
                fontSize: '14px',
            },
        },
        yAxis: {
            name: metrics[1],
            axisLabel: {
                fontSize: '14px',
            },
        },
        tooltip: {
            trigger: 'item',
            formatter: (params: any) => {
                return (`${resolveField(params.value[2]).tag
                    } <br/>${mapMetricToText(metrics[0])}：${keepFixFloat(params.value[0], 2, metrics[0])
                    } <br/>${mapMetricToText(metrics[1])}：${keepFixFloat(params.value[1], 2, metrics[1])}`
                )
            },
        },
        series: [
            {
                data: tags.length > 0 ? tags.map((tag, index) => [data[0][metrics[0]][index], data[0][metrics[1]][index], tag]) : [],
                type: 'scatter',
            },
        ],
    }
    return (
        <EChart
            options={options}
            ref={ref}
            onClick={onclick}
        />
    )
}
export default forwardRef(ScatterChart)

{"compilerOptions": {"baseUrl": ".", "experimentalDecorators": true, "emitDecoratorMetadata": true, "incremental": true, "skipLibCheck": true, "strictNullChecks": true, "noImplicitAny": true, "strictBindCallApply": true, "forceConsistentCasingInFileNames": true, "noFallthroughCasesInSwitch": true, "paths": {"@server/*": ["./apps/x-build-server/src/*"], "@web/*": ["./apps/x-build-web/*"]}}, "exclude": ["node_modules"]}
import { Key, ReactNode, useEffect, useRef, useState } from 'react';
import { Tabs, Button, Input, Tree, Modal, message, Dropdown, Space, Flex, CollapseProps, Collapse } from 'antd';
import { MoreOutlined, PlusOutlined, SearchOutlined, ExclamationCircleOutlined, DeleteOutlined } from '@ant-design/icons';
// import DataPreview from './WorkspaceDataPreview';
import RelationView from './RelationView';
// import FormulaEditorModal from '../components/FormulaEditorModal';
import DatabaseTableCreateModal from './DatabaseTableCreateModal';
import DatabaseTableEditModal from './DatabaseTableEditModal';
import { CustomTreeNode, DatabaseInfo } from '../types';
import { findNodeByKey, tableMenu, updateNodeByKey } from "../utils";
import { useNavigate, useParams } from 'react-router';
import WorkspaceDataPreview from './WorkspaceDataPreview';


const treeData: CustomTreeNode[] = [
	{
		title: '文件夹1',
		key: 'folder1',
		type: 'folder',
		editable: false,
		usedInWorkspace: false,
		children: [
			{
				title: '声量互动量表-数据库',
				key: 'voice-interaction',
				type: 'table',
				tableType: 'db',
				editable: false,
				usedInWorkspace: true,
			},
			{
				title: '互动量表（合并表）-Excel',
				key: 'interaction',
				type: 'table',
				tableType: 'excel',
				editable: false,
				usedInWorkspace: false,
			},
			{
				title: '词云表-数据库',
				key: 'word-cloud',
				type: 'table',
				tableType: 'db',
				editable: false,
				usedInWorkspace: false,
			},
			{
				title: '原文表-Excel',
				key: 'original',
				type: 'table',
				tableType: 'excel',
				editable: false,
				usedInWorkspace: false,
			}
		]
	},
	{
		title: '文件夹2',
		key: 'folder2',
		type: 'folder',
		editable: false,
		usedInWorkspace: false,
		children: [
			{
				title: '舆情互动量-数据库',
				key: 'public-interaction',
				type: 'table',
				tableType: 'db',
				editable: false,
				usedInWorkspace: false,
			},
			{
				title: '舆情比表-Excel',
				key: 'public-comparison',
				type: 'table',
				tableType: 'excel',
				editable: false,
				usedInWorkspace: true,
			}
		]
	},
	{
		title: '文件夹3',
		key: 'folder3',
		type: 'folder',
		editable: false,
		usedInWorkspace: false,
		children: []
	}
];

// 视图树数据
const WorkSpaceDataSet = () => {
	const navigate = useNavigate()
	const params = useParams()
	const [activeTab, setActiveTab] = useState('dataPreview');
	const [activeTree, setActiveTree] = useState('data');
	const [expandedKeys, setExpandedKeys] = useState<Key[]>(['folder1']);
	const [selectedKey, setSelectedKey] = useState<Key>('voice-interaction');
	// 视图相关状态
	const [viewExpandedKeys, setViewExpandedKeys] = useState<Key[]>(['viewFolder1']);
	const [viewSelectedKeys, setViewSelectedKeys] = useState<Key[]>(['chart-view']);
	const [addKey, setAddKey] = useState('')
	const [editingKey, setEditingKey] = useState<string | null>(null);
	const [editingValue, setEditingValue] = useState<string>('');
	const [createDbModalOpen, setCreateDbModalOpen] = useState(false);
	const [editDbModalOpen, setEditDbModalOpen] = useState(false);
	const [messageApi, contextHolder] = message.useMessage();
	const [editDbInitialValues, setEditDbInitialValues] = useState<DatabaseInfo>({
		name: '',
		type: '',
		host: '',
		port: 0,
		username: '',
		password: ''
	});
	const [viewTreeData, setViewTreeData] = useState(() => [
		{
			title: '视图文件夹1',
			key: 'viewFolder1',
			children: [
				{
					title: '柱状图视图',
					key: 'chart-view',
				},
				{
					title: '折线图视图',
					key: 'line-view'
				},
				{
					title: '饼图视图',
					key: 'pie-view'
				}
			]
		},
		{
			title: '视图文件夹2',
			// icon: <FolderOutlined className="mr-1" />,
			key: 'viewFolder2',
			children: [
				{
					title: '散点图视图',
					key: 'scatter-view'
				},
				{
					title: '热力图视图',
					key: 'heatmap-view'
				}
			]
		}
	])
	// 新建文件夹功能
	const handleAddFolder = () => {
		// 统计已有文件夹数量
		const folderCount = treeDataState.filter(node => node.type === 'folder').length;
		const newFolder = {
			title: `文件夹${folderCount + 1}`,
			key: `folder${Date.now()}`,
			type: 'folder',
			editable: false,
			usedInWorkspace: false,
			children: [],
		};
		setTreeData(prev => [...prev, newFolder]);
	};

	// 新建视图文件夹功能
	const handleAddViewFolder = () => {
		const folderCount = viewTreeData.length;
		const newFolder = {
			title: `文件夹${folderCount + 1}`,
			key: `viewFolder${Date.now()}`,
			type: 'folder',
			editable: false,
			usedInWorkspace: false,
			children: [],
		};
		setViewTreeData(prev => [...prev, newFolder]);
	};
	const handleAddDataTable = (folderKey: string) => {
		const newTable: CustomTreeNode = {
			title: `新表-数据库`,
			key: `${folderKey}-new-${Date.now()}`,
			type: 'table',
			tableType: 'db',
			editable: false,
			usedInWorkspace: false,
		};
		setTreeData(prev => updateNodeByKey(prev, folderKey, node => {
			node.children = node.children ? [...node.children, newTable] : [newTable];
		}));
		setAddKey('')
	}
	// 视图选择处理函数
	const handleViewSelect = (selectedKeys: React.Key[]) => {
		setViewSelectedKeys(selectedKeys);
	};
	const removeNode = (nodes: CustomTreeNode[], key: string): CustomTreeNode[] => {
		return nodes.filter(node => {
			if (node.key === key) return false;
			if (node.children) node.children = removeNode(node.children, key);
			return true;
		});
	};
	const handleTableSelect = (selectedKeys: React.Key[]) => {
		setSelectedKey(selectedKeys[0]);
	};
	const handleTabChange = (key: string) => {
		setActiveTab(key);
	};

	// 重命名保存
	const handleRenameSave = (key: string) => {
		if (!editingValue.trim()) {
			messageApi.warning('名称不能为空');
			return;
		}
		treeDataRef.current = updateNodeByKey(treeDataRef.current, key, node => {
			node.title = editingValue;
			node.editable = false;
		});
		setTreeData([...treeDataRef.current]);
		setEditingKey(null);
		setEditingValue('');
	};

	// 删除节点
	const handleDelete = (key: string) => {
		const node = findNodeByKey(treeData, key);
		if (!node) return;
		if (node.type === 'folder') {
			const hasUsed = (node.children || []).some(child => child.usedInWorkspace);
			if (hasUsed) {
				Modal.warning({
					title: '无法删除',
					content: '当前文件夹中有表在工作区中被使用，无法删除！',
				});
				return;
			}
		}
		if (node.type === 'table' && node.usedInWorkspace) {
			Modal.warning({
				title: '无法删除',
				content: '当前表在工作区被使用，请先删除工作区中相关表，再删除该表！',
			});
			return;
		}
		Modal.confirm({
			title: '确认删除',
			content: `是否删除${node.type === 'folder' ? '文件夹' : '表'}"${typeof node.title === 'string' ? node.title : ''}"？`,
			onOk: () => {
				setTreeData(removeNode(treeData, key));
			},
		});
	};

	// treeData状态管理（便于递归操作）
	const [treeDataState, setTreeData] = useState<CustomTreeNode[]>(treeData);
	const treeDataRef = useRef(treeDataState);
	useEffect(() => { treeDataRef.current = treeDataState; }, [treeDataState]);

	// 新增：添加表功能
	const handleAddTable = (folderKey: string, type: 'db' | 'excel') => {
		if (type === 'db') {
			setCreateDbModalOpen(true)
		} else {
			// 跳转到文件上传页面
			navigate(`/workspace/${params.id}/upload`)
		}
		setAddKey(folderKey)
	};
	const handleMoreActions = (nodeData: CustomTreeNode, type: 'rename' | 'delete') => {
		if (type === 'rename') {
			setEditingKey(nodeData.key as string);
			setEditingValue(typeof nodeData.title === 'string' ? nodeData.title : '');
		} else {
			handleDelete(nodeData.key as string)
		}
	}

	// titleRender实现
	const renderCustomNodeTitle = (nodeData: CustomTreeNode) => {
		if (editingKey === nodeData.key) {
			return (
				<Input
					autoFocus
					size="small"
					value={editingValue}
					onChange={e => setEditingValue(e.target.value)}
					onBlur={() => handleRenameSave(nodeData.key as string)}
					onPressEnter={() => handleRenameSave(nodeData.key as string)}
					className='w-full'
				/>
			);
		}
		// 文件夹节点
		if (nodeData.type === 'folder') {
			return (
				<div className="flex items-center justify-between">
					<span onDoubleClick={() => {
						setEditingKey(nodeData.key as string);
						setEditingValue(typeof nodeData.title === 'string' ? nodeData.title : '');
					}}>{nodeData.title as ReactNode}</span>
					<Space>
						<Dropdown
							menu={{
								items: [
									{
										label: "添加数据库表",
										key: "db",
									},
									{
										label: '上传 Excel 文件',
										key: "excel",
									},
								],
								onClick: ({ key }) => handleAddTable(nodeData.key as string, key as 'db' | 'excel'),
							}}
							trigger={['click']}
						>

							<PlusOutlined className={'cursor-pointer'} />
						</Dropdown>
						<Button type="text" size="small" onClick={e => { e.stopPropagation(); handleDelete(nodeData.key as string); }} icon={<DeleteOutlined />} />
					</Space>
				</div>
			);
		}
		// 表节点
		if (nodeData.type === 'table') {
			return (
				<div className="flex items-center group justify-between">
					<span
						style={{
							color: nodeData.usedInWorkspace ? '#faad14' : undefined,
							fontWeight: nodeData.usedInWorkspace ? 600 : undefined,
							marginRight: 4,
						}}
						onDoubleClick={() => {
							setEditingKey(nodeData.key as string);
							setEditingValue(typeof nodeData.title === 'string' ? nodeData.title : '');
						}}
					>
						{nodeData.title as ReactNode}
						{nodeData.tableType ? <span style={{ fontSize: 12, color: '#888', marginLeft: 4 }}>({nodeData.tableType})</span> : null}
						{nodeData.usedInWorkspace && <ExclamationCircleOutlined style={{ color: '#faad14', marginLeft: 4 }} title="被工作区使用" />}
					</span>
					<span className="invisible group-hover:visible">
						<Dropdown
							menu={{
								items: tableMenu,
								onClick: ({ key }) => handleMoreActions(nodeData, key as 'rename' | 'delete')
							}}
							trigger={['hover']}
						>
							<MoreOutlined className="ml-2 cursor-pointer" />
						</Dropdown>
					</span>
				</div>
			);
		}
		// 其他情况
		return <span>{nodeData.title as ReactNode}</span>;
	};
	const collapseItems: CollapseProps['items'] = [
		{
			key: 'public',
			label: '公共表',
			children: <Tree
				// className=""
				treeData={treeDataState}
				// expandedKeys={expandedKeys}
				onSelect={handleTableSelect}
				// onExpand={setExpandedKeys}
				showLine={{ showLeafIcon: false }}
				blockNode
			/>,
		},
		{
			key: 'custom',
			label: '自定义表',
			children: <Tree
				// className="h-64 flex-1 overflow-y-auto"
				treeData={treeDataState}
				expandedKeys={expandedKeys}
				onSelect={handleTableSelect}
				onExpand={setExpandedKeys}
				showLine={{ showLeafIcon: false }}
				blockNode
				titleRender={renderCustomNodeTitle}
			/>,
		},
	];
	return (
		<>
			{contextHolder}
			<div className="flex h-[calc(100vh-64px)]">
				<div className="w-80 bg-white border-r border-gray-200 flex flex-col p-2">
					{/* <div className="flex-1 flex flex-col"> */}
					<Tabs
						className={'rp-[.ant-tabs-content-holder]:flex rp-[.ant-tabs-content-top]:flex flex-1'}
						activeKey={activeTree}
						centered
						onChange={(key: string) => setActiveTree(key)}
						items={[
							{
								key: 'data',
								label: '数据',
								children: (
									<Flex vertical gap={'small'} className={'flex-1 h-full'}>
										<Input
											placeholder="请输入搜索内容"
											prefix={<SearchOutlined />}
										/>
										<div className="flex overflow-y-auto h-[calc(100vh-210px)]">
											<Collapse
												size="small"
												className='flex-1'
												ghost
												items={collapseItems}
											/>
										</div>
										<Button type={'text'} icon={<PlusOutlined />} className="w-full" onClick={() => {
											handleAddFolder()
										}}>
											新建文件夹
										</Button>
									</Flex>
								),
								className: 'flex flex-1',
							},
							{
								key: 'view',
								label: '视图',
								children: (
									<Flex vertical gap={'small'} className={'flex-1 h-full'}>
										<Input
											placeholder="请输入搜索内容"
											prefix={<SearchOutlined />}
										/>
										<Tree
											className="h-[calc(100vh-200px)] flex-1 overflow-y-auto"
											treeData={viewTreeData}
											expandedKeys={viewExpandedKeys}
											selectedKeys={viewSelectedKeys}
											onSelect={handleViewSelect}
											onExpand={setViewExpandedKeys}
											showLine={{ showLeafIcon: false }}
											blockNode
										/>
										<Button type={'text'} icon={<PlusOutlined />} className="w-full" onClick={() => {
											handleAddViewFolder()
										}}>
											新建文件夹
										</Button>
									</Flex>
								),
								className: 'flex flex-1',
							}
						]}
					/>
					{/* </div> */}
				</div>
				<div className="flex-1 mx-2 flex flex-col">
					<div className="flex justify-between items-center my-2">
						<Space>
							<Button onClick={() => {
								navigate(`/workspace/${params.id}/addView`)
							}}>
								新建视图
							</Button>
							{
								activeTree === 'view' && <Button>
									编辑视图
								</Button>
							}
						</Space>
						{
							activeTree === 'data' && treeDataState.find(node => node.key === selectedKey)?.tableType === 'db' ?
								<Button onClick={() => {
									setEditDbInitialValues({
										name: '数据库名称',
										type: 'mysql',
										host: '主机',
										port: 0,
										username: '用户名',
										password: '密码',
									});
									setEditDbModalOpen(true);
								}}>
									编辑数据库表
								</Button>
								: <Button>
									追加数据
								</Button>
						}
					</div>
					<Tabs
						activeKey={activeTab}
						// centered
						className={'rp-[.ant-tabs-content-holder]:flex rp-[.ant-tabs-content-top]:flex flex-1'}
						onChange={handleTabChange}
						items={[
							{
								key: 'dataPreview',
								label: '数据预览',
								children: <WorkspaceDataPreview />,
								className: 'flex flex-1',

							},
							{
								key: 'relatedView',
								label: '关联视图',
								children: <RelationView />,
								className: 'flex flex-1',
							}
						]}
					/>
				</div>
				<DatabaseTableCreateModal
					open={createDbModalOpen}
					onOk={() => {
						handleAddDataTable(addKey);
						setCreateDbModalOpen(false);
						messageApi.success('创建成功');
					}}
					onCancel={() => {
						setAddKey('')
						setCreateDbModalOpen(false)
					}}
					onTestConnection={() => messageApi.success('连接成功')}
				/>
				<DatabaseTableEditModal
					open={editDbModalOpen}
					initialValues={editDbInitialValues}
					onOk={() => { setEditDbModalOpen(false); messageApi.success('编辑成功'); }}
					onCancel={() => setEditDbModalOpen(false)}
					onTestConnection={() => messageApi.success('连接成功')}
				/>

			</div>
		</>
	)
}
export default WorkSpaceDataSet
import type { LineChartProps } from '@web/src/types'
import { forwardRef } from 'react'
import { keepFixFloat, mapMetricToText, resolveField } from '@web/src/utils'
import EChart from './EChart'
import { isEmpty } from 'radash'

function LineChart({
    data,
    areaStyle,
    metrics,
    period,
    views,
    tags,
    tagDims,
    path,
    paths,
    chartId,
    setPath,
    drillDown,
}: LineChartProps, ref: any) {
    // console.log(metrics);
    if (metrics.length !== 1) {
        return <div>请选择一个指标</div>
    }
    if (!data.length || isEmpty(data[0])) {
        return <div>暂无数据</div>
    }
    const onclick = (params: any) => {
        chartId && drillDown && drillDown(chartId, params.name, tagDims)
        path && setPath && setPath([...path, params.name])
    }
    const options = (paths && paths.find((item: any) => item.id === chartId).path.length > 0) || (path && path.length > 0)
        ? {
            tooltip: {
                trigger: 'axis',
                // show:views.length<=1,
                formatter: (params: any) => {
                    if (views.length > 1) {
                        // console.log(params);
                        return `${resolveField(params[0].name).tag}<br/>${params.map((i: any) => `${i.marker}${resolveField(i.seriesName).tag} ${mapMetricToText(metrics[0])}：${keepFixFloat(i.value, 2, metrics[0])}`).join('<br/>')}`
                    }
                    if (period && period.length > 0) {
                        return `${params[0].name}<br/>${params.map((i: any) => `${i.marker}${resolveField(i.seriesName).tag} ${mapMetricToText(metrics[0])}：${keepFixFloat(i.value, 2, metrics[0])}`).join('<br/>')}`
                    }
                    return `${resolveField(params[0].name).tag}<br/> ${mapMetricToText(metrics[0])}：${keepFixFloat(params[0].value, 2, metrics[0])}`
                },
            },
            // title: {
            //     text: name || '',
            // },
            legend: {
                textStyle: {
                    fontSize: '14px',
                },
                // top: 'center',
                // left: 'right',
                // orient: 'vertical',
                type: 'scroll',
                formatter: (name: string) => resolveField(name).tag,
            },
            xAxis: {
                type: 'category',
                axisLabel: {
                    interval: 0,
                    rotate: -40,
                    fontSize: '14px',
                    formatter: (value: string) => resolveField(value).tag,
                },
                data: views.length > 1
                    ? views.map(view => view.name)
                    : period && period.length > 0
                        ? period.map((date: any) => date.toString())
                        : tags,
            },
            yAxis: {
                type: 'value',
                axisLabel: {
                    fontSize: '14px',
                    formatter: (value: string) => keepFixFloat(value, 2, metrics[0]),
                },
            },
            series:
                views.length > 1
                    ? tags.map((tag, index) => ({
                        type: 'bar',
                        name: tag,
                        emphasis: {
                            focus: 'series',
                        },
                        markLine: {
                            data: [{
                                type: 'average',
                                name: '平均值',
                                label: {
                                    fontSize: '13px',
                                    formatter: (params: any) => keepFixFloat(params.value, 2, metrics[0]),
                                },
                            }],
                        },
                        realtimeSort: true,
                        data: views.map((_, index2) => data[index2][metrics[0]][index]),
                    }))
                    : period && period.length > 0
                        ? tags.map((tag, index) => ({
                            type: 'bar',
                            name: tag,
                            emphasis: {
                                focus: 'series',
                            },
                            markLine: {
                                data: [{
                                    type: 'average',
                                    name: '平均值',
                                    label: {
                                        fontSize: '13px',
                                        formatter: (params: any) => keepFixFloat(params.value, 2, metrics[0]),
                                    },
                                }],
                            },
                            // realtimeSort: true,
                            data: data[0][metrics[0]][index],
                        }))
                        : [{
                            type: 'bar',
                            realtimeSort: true,
                            markLine: {
                                data: [{
                                    type: 'average',
                                    name: '平均值',
                                    label: {
                                        fontSize: '13px',
                                        formatter: (params: any) => keepFixFloat(params.value, 2, metrics[0]),
                                    },
                                }],
                            },
                            data: data[0][metrics[0]],
                        }],
        }
        : {
            tooltip: {
                trigger: 'axis',
                // show:views.length<=1,
                formatter: (params: any) => {
                    if (views.length > 1) {
                        // console.log(params);
                        return `${resolveField(params[0].name).tag}<br/>${params.map((i: any) => `${i.marker}${resolveField(i.seriesName).tag} ${mapMetricToText(metrics[0])}：${keepFixFloat(i.value, 2, metrics[0])}`).join('<br/>')}`
                    }
                    if (period && period.length > 0) {
                        return `${params[0].name}<br/>${params.map((i: any) => `${i.marker}${resolveField(i.seriesName).tag} ${mapMetricToText(metrics[0])}：${keepFixFloat(i.value, 2, metrics[0])}`).join('<br/>')}`
                    }
                    return `${resolveField(params[0].name).tag}<br/> ${mapMetricToText(metrics[0])}：${keepFixFloat(params[0].value, 2, metrics[0])}`
                },
            },
            // title: {
            //     text: name || '',
            // },
            legend: {
                textStyle: {
                    fontSize: '14px',
                },
                show: views.length > 1,
                data: views.map(view => view.name),
                // top: 'center',
                // left: 'right',
                // orient: 'vertical',
                type: 'scroll',
                // formatter: (name: string) => name
            },
            xAxis: {
                type: 'category',
                data: period && period.length > 0
                    ? period
                    : tags,
                axisLabel: {
                    interval: 0,
                    rotate: -40,
                    fontSize: '14px',
                    formatter: (value: string) => resolveField(value).tag,
                },
                boundaryGap: false,
            },
            yAxis: {
                type: 'value',
                axisLabel: {
                    fontSize: '14px',
                    formatter: (value: string) => keepFixFloat(value, 2, metrics[0]),
                },
            },
            series:
                views.length > 1
                    ? views.map((view, index) => ({
                        type: 'line',
                        name: view.name,
                        markLine: {
                            data: [{
                                type: 'average',
                                name: '平均值',
                                label: {
                                    fontSize: '13px',
                                    formatter: (params: any) => keepFixFloat(params.value, 2, metrics[0]),
                                },
                            }],
                        },
                        emphasis: {
                            focus: 'series',
                        },
                        data: period && period.length > 0 ? data[index][metrics[0]][0] : data[index][metrics[0]],
                        smooth: true,
                        areaStyle: areaStyle || null,
                    }))
                    : period && period.length > 0
                        ? tags.map((tag, index) => ({
                            type: 'line',
                            name: tag,
                            emphasis: {
                                focus: 'series',
                            },
                            markLine: {
                                data: [{
                                    type: 'average',
                                    name: '平均值',
                                    label: {
                                        fontSize: '13px',
                                        formatter: (params: any) => keepFixFloat(params.value, 2, metrics[0]),
                                    },
                                }],
                            },
                            data: data[0][metrics[0]][index],
                            smooth: true,
                            areaStyle: areaStyle || null,
                        }))
                        : [{
                            type: 'line',
                            data: data[0][metrics[0]],
                            emphasis: {
                                focus: 'series',
                            },
                            markLine: {
                                data: [{
                                    type: 'average',
                                    name: '平均值',
                                    label: {
                                        fontSize: '13px',
                                        formatter: (params: any) => keepFixFloat(params.value, 2, metrics[0]),
                                    },
                                }],
                            },
                            smooth: true,
                            areaStyle: areaStyle || null,
                        }],
        }
    return (
        <EChart
            options={options}
            ref={ref}
            onClick={onclick}
        />
    )
}
export default forwardRef(LineChart)

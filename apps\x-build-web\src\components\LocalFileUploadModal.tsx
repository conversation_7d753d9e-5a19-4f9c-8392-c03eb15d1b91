import React, { useState } from 'react';
import { Modal, Form, Input, Upload, Button, Space, message, UploadFile, UploadProps } from 'antd';
import { UploadOutlined } from '@ant-design/icons';
// import * as XLSX from 'xlsx';
export interface LocalFileInfo {
	fileList: UploadFile[];
	name: string;
	description?: string;
}

interface LocalFileUploadModalProps {
	open: boolean;
	onOk: (fileInfo: LocalFileInfo) => void;
	onCancel: () => void;
	confirmLoading?: boolean;
}

const LocalFileUploadModal: React.FC<LocalFileUploadModalProps> = ({ open, onOk, onCancel, confirmLoading }) => {
	const [form] = Form.useForm<LocalFileInfo>();
	const [fileList, setFileList] = useState<UploadFile[]>([]);
	const [messageApi, contextHolder] = message.useMessage();
	const beforeUpload = (file: any) => {
		const fileExtension = file.name.split('.').pop().toLowerCase();
		// 定义允许的扩展名
		const allowedExtensions = ['csv'];
		// 校验文件扩展名
		if (!allowedExtensions.includes(fileExtension)) {
			message.error('只允许上传CSV格式的文件');
			return false; // 阻止上传
		}
		if(file.size /1024 /1024 >10) {
			messageApi.error('文件大小不能超过10MB');
			return false;
		}
		// form.setFieldsValue({ name: file.name });
		return false; // 阻止自动上传
	};

	// const handleRemove = (file:UploadFile) => {
	// 	// setFile(null);
	// 	setFileList(fileList.filter(f=>file.uid!==f.uid))
	// };
	const handleChange: UploadProps['onChange'] = ({ fileList: newFileList }) => {
		setFileList(newFileList);
	};
	// const handleFileUpload = (file: File): boolean => {
	// 	if (!file) {
	// 		messageApi.error('未选择文件');
	// 		return false;
	// 	}
	// 	const reader = new FileReader();
	// 	reader.onload = (e: ProgressEvent<FileReader>) => {
	// 		try {
	// 			const binaryStr = e.target?.result;
	// 			if (typeof binaryStr !== 'string' && !(binaryStr instanceof ArrayBuffer)) {
	// 				messageApi.error('文件读取失败');
	// 				return;
	// 			}
	// 			const workbook = XLSX.read(binaryStr, { type: 'binary' });
	// 			const firstSheetName = workbook.SheetNames?.[0];
	// 			if (!firstSheetName) {
	// 				messageApi.warning('Excel 文件没有工作表');
	// 				return;
	// 			}
	// 			const worksheet = workbook.Sheets[firstSheetName];
	// 			if (!worksheet) {
	// 				messageApi.warning('Excel 工作表内容为空');
	// 				return;
	// 			}
	// 			const jsonData: Record<string, object>[] = XLSX.utils.sheet_to_json(worksheet, { defval: '' });

	// 			if (!Array.isArray(jsonData) || jsonData.length === 0) {
	// 				messageApi.warning('Excel 文件内容为空');
	// 				return;
	// 			}

	// 			const firstRow = jsonData[0];
	// 			if (typeof firstRow !== 'object' || firstRow === null) {
	// 				messageApi.warning('Excel 数据格式异常');
	// 				return;
	// 			}

	// 			const cols = Object.keys(firstRow).map((key) => ({
	// 				title: key,
	// 				dataIndex: key,
	// 				key,
	// 			}));

	// 			const dataWithKeys = jsonData.map((row, index) => {
	// 				if (typeof row !== 'object' || row === null) {
	// 					return { key: index };
	// 				}
	// 				return {
	// 					key: index,
	// 					...row,
	// 				};
	// 			});
	// 			// console.log(cols);
	// 			// console.log(dataWithKeys);
	// 			// setColumns(cols);
	// 			// setDataSource(dataWithKeys);
	// 			messageApi.success('文件解析成功');
	// 		} catch (error) {
	// 			messageApi.error('文件解析失败，请确认文件格式是否正确');
	// 			console.error(error);
	// 		}
	// 	};
	// 	reader.onerror = () => {
	// 		messageApi.error('文件读取失败');
	// 	};
	// 	reader.readAsArrayBuffer(file);
	// 	return false; // 阻止 Upload 组件自动上传
	// };
	const handleOk = async () => {
		const values = await form.validateFields();
		if (!fileList.length) {
			message.warning('请先选择文件');
			return;
		}
		onOk({ fileList, name: values.name, description: values.description });
		form.resetFields();
		// setFile(null);
	};

	return (
		<>
			{contextHolder}
			<Modal
				open={open}
				title={<span style={{ fontWeight: 700, fontSize: 24 }}>上传本地文件</span>}
				onCancel={() => {
					form.resetFields();
					setFileList([]);
					onCancel();
				}}
				footer={
					<Space>
						<Button onClick={() => {
							form.resetFields();
							setFileList([]);
							onCancel();
						}}>
							取消
						</Button>
						<Button type="primary" htmlType="submit" loading={confirmLoading} onClick={handleOk} disabled={!fileList.length}>
							上传
						</Button>
					</Space>
				}
				width={500}
			>
				<Form
					form={form}
					layout="vertical"
					onFinish={handleOk}
				>
					<Form.Item label="选择文件" required>
						<Upload.Dragger
							beforeUpload={beforeUpload}
							// onRemove={handleRemove}
							onChange={handleChange}
							fileList={fileList}
							accept={'.csv'}
							maxCount={10}
							pastable
							multiple
							progress={{
								strokeColor: {
									'0%': '#108ee9',
									'100%': '#87d068',
								},
								strokeWidth: 3,
								format: (percent) => percent && `${parseFloat(percent.toFixed(2))}%`,
							}}
							showUploadList={{ showRemoveIcon: true }}
						>
							<p className="ant-upload-drag-icon">
								<UploadOutlined />
							</p>
							<p className="ant-upload-text">点击，粘贴或拖拽文件到此处上传</p>
							<p className="ant-upload-hint">多选，支持CSV文件</p>
						</Upload.Dragger>
					</Form.Item>
					<Form.Item name="name" label="文件名" rules={[{ required: true, message: '请输入文件名' }]}
						initialValue={''}
					>
						<Input placeholder="请输入文件名" />
					</Form.Item>
					<Form.Item name="description" label="描述">
						<Input.TextArea placeholder="可选，添加文件描述" rows={2} />
					</Form.Item>
				</Form>
			</Modal>
		</>
	);
};

export default LocalFileUploadModal; 
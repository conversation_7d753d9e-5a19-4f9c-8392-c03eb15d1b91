import type { FunnelChartProps } from '@web/src/types'
import { forwardRef } from 'react'
import { keepFixFloat, mapMetricToText, resolveField } from '@web/src/utils'
import EChart from './EChart'
import { isEmpty } from 'radash'

function FunnelChart({ data, metrics, tags, tagDims, path, chartId, paths, setPath, drillDown }: FunnelChartProps, ref: any) {
    if (metrics.length !== 1) {
        return <div>请选择一个指标</div>
    }
    if (!data.length || isEmpty(data[0])) {
        return <div>暂无数据</div>
    }
    const onclick = (params: any) => {
        chartId && drillDown && drillDown(chartId, params.name, tagDims)
        path && setPath && setPath([...path, params.name])
    }
    const options = (paths && paths.find((item: any) => item.id === chartId).path.length > 0) || (path && path.length > 0)
        ? {
            tooltip: {
                trigger: 'axis',
                formatter: (params: any) => `${resolveField(params.name).tag}<br/>${params.marker} ${mapMetricToText(metrics[0])}：${keepFixFloat(params.value, 2, metrics[0])}`,
            },
            // title: {
            //     text: name || '',
            // },
            legend: {
                textStyle: {
                    fontSize: '14px',
                },
                // top: 'center',
                // left: 'right',
                // orient: 'vertical',
                type: 'scroll',
                formatter: (name: string) => resolveField(name).tag,
                // data:tags
            },
            xAxis: {
                type: 'category',
                axisLabel: {
                    interval: 0,
                    rotate: -40,
                    fontSize: '14px',
                    formatter: (value: string) => resolveField(value).tag,
                },
                data: tags,
            },
            yAxis: {
                type: 'value',
                axisLabel: {
                    fontSize: '14px',
                },
            },
            series: [{
                type: 'bar',
                realtimeSort: true,
                data: data[0][metrics[0]] ?? [],
            }],
        }
        : {
            legend: {
                textStyle: {
                    fontSize: '14px',
                },
                data: tags,
                // top: 'center',
                // left: 'right',
                // orient: 'vertical',
                type: 'scroll',
                formatter: (name: string) => resolveField(name).tag,
            },
            // title: {
            //     text: name || '',
            // },
            tooltip: {
                trigger: 'item',
                formatter: (params: any) => `${resolveField(params.name).tag}<br/>${params.marker} ${mapMetricToText(metrics[0])}：${keepFixFloat(params.value, 2, metrics[0])}`,
            },
            series: [{
                type: 'funnel',
                gap: 2,
                label: {
                    show: true,
                    position: 'inside',
                    formatter: (params: any) => resolveField(params.name).tag,
                },
                sort: 'descending',
                labelLine: {
                    length: 10,
                    lineStyle: {
                        width: 1,
                        type: 'solid',
                    },
                },
                itemStyle: {
                    borderColor: '#fff',
                    borderWidth: 1,
                },
                emphasis: {
                    label: {
                        fontSize: 20,
                    },
                },
                data: data[0][metrics[0]]?.map((item: any, index: number) => ({ name: tags[index], value: item })),
            }],
        }
    return (
        <EChart
            options={options}
            ref={ref}
            onClick={onclick}
        />
    )
}
export default forwardRef(FunnelChart)

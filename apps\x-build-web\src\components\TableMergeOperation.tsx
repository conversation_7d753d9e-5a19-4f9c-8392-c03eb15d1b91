import { PlusOutlined, DeleteOutlined } from "@ant-design/icons";
import { Button, Select, Card } from "antd";

// 合并依据类型定义
export type JoinCondition = {
  id: string;
  sourceField: string;  // 原表字段
  targetField: string;  // 目标表字段
};

// 表合并配置类型定义
export type TableMergeConfig = {
  id: string;
  targetTable: string;  // 要合并的表
  mergeType: 'left' | 'right' | 'inner' | 'outer';  // 合并方式
  joinConditions: JoinCondition[];  // 合并依据
};

// 组件Props类型定义
interface TableMergeOperationProps {
  tableMergeConfigs: TableMergeConfig[];
  setTableMergeConfigs: React.Dispatch<React.SetStateAction<TableMergeConfig[]>>;
  availableTables: string[];  // 可用的表列表
  fieldKeys: string[];  // 当前表字段
  getTableFields: (tableName: string) => string[];  // 获取指定表的字段
};

// 合并方式选项
const mergeTypeOptions = [
  { label: '左合并', value: 'left' },
  { label: '右合并', value: 'right' },
  { label: '交集合并', value: 'inner' },
  { label: '并集合并', value: 'outer' }
];

const TableMergeOperation = ({
  tableMergeConfigs,
  setTableMergeConfigs,
  availableTables,
  fieldKeys,
  getTableFields
}: TableMergeOperationProps) => {



  // 添加新的合并配置
  const addMergeConfig = () => {
    const newConfig: TableMergeConfig = {
      id: Date.now().toString(),
      targetTable: '',
      mergeType: 'left',
      joinConditions: []
    };
    setTableMergeConfigs(prev => [...prev, newConfig]);
  };

  // 删除合并配置
  const removeMergeConfig = (configId: string) => {
    setTableMergeConfigs(prev => prev.filter(config => config.id !== configId));
  };

  // 更新合并配置
  const updateMergeConfig = (configId: string, updates: Partial<TableMergeConfig>) => {
    setTableMergeConfigs(prev => 
      prev.map(config => 
        config.id === configId ? { ...config, ...updates } : config
      )
    );
  };

  // 添加合并依据
  const addJoinCondition = (configId: string) => {
    const newCondition: JoinCondition = {
      id: Date.now().toString(),
      sourceField: '',
      targetField: ''
    };
    
    setTableMergeConfigs(prev => 
      prev.map(config => 
        config.id === configId 
          ? { ...config, joinConditions: [...config.joinConditions, newCondition] }
          : config
      )
    );
  };

  // 删除合并依据
  const removeJoinCondition = (configId: string, conditionId: string) => {
    setTableMergeConfigs(prev => 
      prev.map(config => 
        config.id === configId 
          ? { 
              ...config, 
              joinConditions: config.joinConditions.filter(condition => condition.id !== conditionId) 
            }
          : config
      )
    );
  };

  // 更新合并依据
  const updateJoinCondition = (configId: string, conditionId: string, updates: Partial<JoinCondition>) => {
    setTableMergeConfigs(prev => 
      prev.map(config => 
        config.id === configId 
          ? {
              ...config,
              joinConditions: config.joinConditions.map(condition =>
                condition.id === conditionId ? { ...condition, ...updates } : condition
              )
            }
          : config
      )
    );
  };

  return (
    <div className="mb-2 p-2 rounded-md">
      <div className="flex items-center justify-between mb-3">
        <span className="font-bold">表合并</span>
        <Button
          type="primary"
          size="small"
          icon={<PlusOutlined />}
          onClick={addMergeConfig}
        >
          添加合并表
        </Button>
      </div>

      {tableMergeConfigs.length === 0 ? (
        <div className="text-gray-500 text-center py-4">
          暂无合并配置，点击"添加合并表"开始设置表合并规则
        </div>
      ) : (
        <div className="space-y-4">
          {tableMergeConfigs.map((config, index) => (
            <Card 
              key={config.id} 
              size="small" 
              className="bg-white"
              title={
                <div className="flex items-center justify-between">
                  <span className="text-blue-600">表合并 {index + 1}</span>
                  <Button
                    type="text"
                    size="small"
                    icon={<DeleteOutlined />}
                    onClick={() => removeMergeConfig(config.id)}
                    danger
                    title="删除此合并配置"
                  />
                </div>
              }
            >
              <div className="space-y-3">
                {/* 表选择和合并方式 */}
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium mb-1">表：</label>
                    <Select
                      placeholder="请选择要合并的表"
                      value={config.targetTable || undefined}
                      onChange={(value) => updateMergeConfig(config.id, { targetTable: value })}
                      className="w-full"
                      options={availableTables.map(table => ({
                        label: table,
                        value: table
                      }))}
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium mb-1">合并方式：</label>
                    <Select
                      placeholder="请选择合并方式"
                      value={config.mergeType}
                      onChange={(value) => updateMergeConfig(config.id, { mergeType: value })}
                      className="w-full"
                      options={mergeTypeOptions}
                    />
                  </div>
                </div>

                {/* 合并依据 */}
                <div>
                  <div className="flex items-center justify-between mb-2">
                    <label className="text-sm font-medium">合并依据：</label>
                    <Button
                      type="link"
                      size="small"
                      icon={<PlusOutlined />}
                      onClick={() => addJoinCondition(config.id)}
                      className="text-blue-500"
                    >
                      添加合并依据
                    </Button>
                  </div>

                  {config.joinConditions.length === 0 ? (
                    <div className="text-gray-400 text-sm text-center py-2 border border-dashed rounded">
                      点击"添加合并依据"设置字段映射关系
                    </div>
                  ) : (
                    <div className="space-y-2">
                      {config.joinConditions.map((condition) => (
                        <div key={condition.id} className="flex items-center space-x-2 p-2 bg-gray-50 rounded">
                          <div className="flex-1">
                            <Select
                              placeholder="原表字段"
                              value={condition.sourceField || undefined}
                              onChange={(value) => updateJoinCondition(config.id, condition.id, { sourceField: value })}
                              className="w-full"
                              size="small"
                              options={fieldKeys.map(field => ({
                                label: field,
                                value: field
                              }))}
                            />
                          </div>
                          <span className="text-gray-500 text-sm">=</span>
                          <div className="flex-1">
                            <Select
                              placeholder="合并表字段"
                              value={condition.targetField || undefined}
                              onChange={(value) => updateJoinCondition(config.id, condition.id, { targetField: value })}
                              className="w-full"
                              size="small"
                              options={config.targetTable ? getTableFields(config.targetTable).map(field => ({
                                label: field,
                                value: field
                              })) : []}
                              disabled={!config.targetTable}
                            />
                          </div>
                          
                          <Button
                            type="text"
                            size="small"
                            icon={<DeleteOutlined />}
                            onClick={() => removeJoinCondition(config.id, condition.id)}
                            danger
                            title="删除此合并依据"
                          />
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            </Card>
          ))}
        </div>
      )}

      {/* 操作说明 */}
      {tableMergeConfigs.length > 0 && (
        <div className="text-xs text-gray-500 mt-3">
          <div>• 每点击一次合并，增加一个表合并区域</div>
          <div>• 选择要合并的表：左合并、右合并、交集合并、并集合并</div>
          <div>• 点击下拉框选择原表字段与合并表字段</div>
          <div>• 点击删除，删除对应合并依据</div>
        </div>
      )}
    </div>
  );
};

export default TableMergeOperation;

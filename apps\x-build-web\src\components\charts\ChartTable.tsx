import type { ChartTableProps, TgiTableData } from '@web/src/types'
// import { ThemeContext } from '@/store/ThemeContext'
import { ConfigProvider, Table } from 'antd'
import { memo, useEffect, useRef, useState } from 'react'
import { keepFixFloat, resolveField, tgiSorter } from '@web/src/utils'
import { useSize } from 'ahooks'
import { isEmpty } from 'radash'

function ChartTable({ data, metrics, views, tags, period }: ChartTableProps) {
    if (!data.length || isEmpty(data[0])) {
        return <div>暂无数据</div>
    }
    if (metrics.length === 0) {
        return <div>请选择至少一个指标</div>
    }
    // const { themeValue } = useContext(ThemeContext)
    const containerRef = useRef<HTMLDivElement>(null)
    const size = useSize(containerRef)
    const [tableHeight, setTableHeight] = useState(200)
    useEffect(() => {
        if (size?.height) {
            const headerHeight = containerRef.current?.firstElementChild?.getElementsByClassName('ant-table-header')[0]?.clientHeight || 39;
            // const pagiHeight = target.current?.firstElementChild?.getElementsByClassName('ant-table-pagination')[0]?.clientHeight || 0;
            const newHeight = size.height - headerHeight;
            setTableHeight(Math.round(newHeight));
        }
    }, [size?.height]);

    const dataSource: any[] = []
    period && period.length
        ? period.forEach((time, index) => {
            tags.forEach((tag, index2) => (
                dataSource.push({
                    period: time,
                    tag,
                    key: `${index}-${index2}`,
                    ...data.reduce((acc: any, obj: any, index3: any) => {
                        acc[index3] = Object.keys(obj).reduce((newObj: any, key: any) => {
                            newObj[key] = obj[key][index2] ? obj[key][index2][index] : obj[key][index2]
                            return newObj
                        }, {})
                        return acc
                    }, {}),
                })
            ))
        })
        : tags.forEach((tag, index2) => (
            dataSource.push({
                tag,
                key: `${index2}`,
                ...data.reduce((acc: any, obj: any, index3: any) => {
                    acc[index3] = Object.keys(obj).reduce((newObj: any, key: any) => {
                        newObj[key] = obj[key][index2]
                        return newObj
                    }, {})
                    return acc
                }, {}),
            })
        ))
    const columns = [
        {
            title: 'period',
            dataIndex: 'period',
            hidden: period && period.length === 0,
            render: (text: any) => (
                <div
                // style={{ color: themeValue?.title?.textStyle?.color || '#333' }}
                >
                    {text}
                </div>
            ),
        },
        {
            title: 'tag',
            dataIndex: 'tag',
            render: (text: any) => (
                <div
                // style={{ color: themeValue?.title?.textStyle?.color || '#333' }}
                >
                    {resolveField(text).tag}
                </div>
            ),
        },
        ...views.map((view, index) => ({
            title: view.name,
            children: metrics.map((metric: any) => ({
                title: metric,
                dataIndex: [`${index}`, metric],
                key: metric,
                sorter: (a: TgiTableData, b: TgiTableData) => tgiSorter(a, b, [`${index}`, metric]),
                render: (text: any) => (
                    <div
                    // style={{ color: themeValue?.title?.textStyle?.color || '#333' }}
                    >
                        {keepFixFloat(text, 2, metric) || '--'}
                    </div>
                ),
            })),
        })),
    ]
    return (
        <div
            ref={containerRef}
            style={{ width: '100%', height: '100%' }}
        >
            <ConfigProvider
                theme={{
                    token: {
                        // colorBgContainer: themeValue.backgroundColor || '#fff',
                    },
                    components: {
                        Table: {
                            // headerColor: themeValue.title?.textStyle?.color || '#333',
                        },
                    },
                }}
            >
                <Table
                    bordered
                    // tableLayout="auto"
                    sticky
                    scroll={{
                        y: tableHeight,
                    }}
                    dataSource={dataSource}
                    columns={columns}
                    rowKey="key"
                    pagination={false}
                // style={{ width:size.width, height: size.height}}
                />
            </ConfigProvider>
        </div>
    )
}
export default memo(ChartTable)

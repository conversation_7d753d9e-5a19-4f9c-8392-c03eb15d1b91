{"name": "x-build-web", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@ant-design/cssinjs": "^1.23.0", "@ant-design/icons": "^6.0.0", "@ant-design/pro-components": "^2.8.7", "@tanstack/react-query": "^5.74.3", "@trpc/react-query": "^11.1.2", "@trpc/tanstack-react-query": "^11.1.2", "@xyflow/react": "^12.6.4", "ahooks": "^3.8.5", "antd": "^5.24.6", "chevrotain": "^11.0.3", "echarts": "^5.6.0", "echarts-for-react": "^3.0.2", "echarts-wordcloud": "^2.1.0", "nanoid": "^5.1.5", "radash": "^12.1.1", "react": "^18.3.0", "react-dom": "^18.3.0", "react-router": "^7.5.1", "react-router-dom": "^7.5.1", "tailwindcss": "3", "xlsx": "^0.18.5"}, "devDependencies": {"@eslint/js": "^9.21.0", "@types/react": "^18.3.0", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.21", "eslint": "^9.21.0", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^15.15.0", "postcss": "^8.5.3", "typescript": "^5.7.3", "typescript-eslint": "^8.24.1", "vite": "^6.2.0"}}
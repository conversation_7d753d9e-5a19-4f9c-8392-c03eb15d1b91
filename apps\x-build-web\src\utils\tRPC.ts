import { QueryClient } from '@tanstack/react-query';
import { createTR<PERSON><PERSON>lient, httpLink } from '@trpc/client';
import { createTRPCOptionsProxy } from '@trpc/tanstack-react-query';
import type { AppRouter } from '@server/@generated/server';
export const queryClient = new QueryClient();
const trpcClient = createTRPCClient<AppRouter>({
	links: [httpLink({ url: 'http://localhost:3000/trpc' })],
});
export const trpc = createTRPCOptionsProxy<AppRouter>({
	client: trpcClient,
	queryClient,
});
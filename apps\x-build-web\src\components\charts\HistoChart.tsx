import type { HistoChartProps } from '@web/src/types'
import { forwardRef } from 'react'
import { keepFixFloat, mapMetricToText, resolveField } from '@web/src/utils'
import EChart from './EChart'
import { isEmpty } from 'radash'

function HistoChart({ data, tags, metrics, views, period, tagDims, path, chartId, setPath, drillDown }: HistoChartProps, ref: any) {
    if (metrics.length !== 1) {
        return <div>请选择一个指标</div>
    }
    if (!data.length || isEmpty(data[0])) {
        return <div>暂无数据</div>
    }
    const onclick = (params: any) => {
        chartId && drillDown && drillDown(chartId, params.name, tagDims)
        path && setPath && setPath([...path, params.name])
    }
    const options = {
        tooltip: {
            trigger: 'axis',
            // show:views.length<=1,
            formatter: (params: any) => {
                if (views.length > 1) {
                    // console.log(params);
                    return `${resolveField(params[0].name).tag}<br/>${params.map((i: any) => `${i.marker}${resolveField(i.seriesName).tag} ${mapMetricToText(metrics[0])}：${keepFixFloat(i.value, 2, metrics[0])}`).join('<br/>')}`
                }
                if (period && period.length > 0) {
                    return `${params[0].name}<br/>${params.map((i: any) => `${i.marker}${resolveField(i.seriesName).tag} ${mapMetricToText(metrics[0])}：${keepFixFloat(i.value, 2, metrics[0])}`).join('<br/>')}`
                }
                return `${resolveField(params[0].name).tag}<br/> ${mapMetricToText(metrics[0])}：${keepFixFloat(params[0].value, 2, metrics[0])}`
            },
        },
        // title: {
        //     text: name || '',
        // },
        xAxis: {
            type: 'value',
            axisLabel: {
                fontSize: '14px',
                formatter: (value: string) => keepFixFloat(value, 2, metrics[0]),
            },
        },
        yAxis: {
            type: 'category',
            inverse: true,
            axisLabel: {
                interval: 0,
                rotate: -40,
                fontSize: '14px',
                formatter: (value: string) => resolveField(value).tag,
            },
            data: views.length > 1
                ? views.map(view => view.name)
                : period && period.length > 0
                    ? period
                    : tags,
        },
        series:
            views.length > 1
                ? tags.map((tag, index) => ({
                    type: 'bar',
                    name: tag,
                    emphasis: {
                        focus: 'series',
                    },
                    realtimeSort: true,
                    markLine: {
                        data: [{
                            type: 'average',
                            name: '平均值',
                            label: {
                                fontSize: '13px',
                                formatter: (params: any) => keepFixFloat(params.value, 2, metrics[0]),
                            },
                        }],
                    },
                    data: views.map((_, index2) => data[index2][metrics[0]][index]),
                }))
                : period && period.length > 0
                    ? tags.map((tag, index) => ({
                        type: 'bar',
                        name: tag,
                        emphasis: {
                            focus: 'series',
                        },
                        markLine: {
                            data: [{
                                type: 'average',
                                name: '平均值',
                                label: {
                                    fontSize: '13px',
                                    formatter: (params: any) => keepFixFloat(params.value, 2, metrics[0]),
                                },
                            }],
                        },
                        // realtimeSort: true,
                        data: data[0][metrics[0]][index],
                    }))
                    : [{
                        type: 'bar',
                        realtimeSort: true,
                        markLine: {
                            data: [{
                                type: 'average',
                                name: '平均值',
                                label: {
                                    fontSize: '13px',
                                    formatter: (params: any) => keepFixFloat(params.value, 2, metrics[0]),
                                },
                            }],
                        },
                        data: data[0][metrics[0]],
                    }],
    }
    return (
        <EChart
            options={options}
            ref={ref}
            onClick={onclick}
        />
    )
}
export default forwardRef(HistoChart)

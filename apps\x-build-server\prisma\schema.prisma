generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Tenant {
  id          String      @id @db.Uuid
  name        String      @db.VarChar(100)
  created_at  DateTime    @default(now()) @db.Timestamptz(6)
  updated_at  DateTime    @updatedAt @db.Timestamptz(6)
  app_domains AppDomain[]
  users       User[]

  @@map("tenants")
}

model User {
  id         String     @id @db.VarChar(100)
  name       String     @db.VarChar(100)
  created_at DateTime   @default(now()) @db.Timestamptz(6)
  updated_at DateTime   @updatedAt @db.Timestamptz(6)
  tenant_id  String     @db.Uuid
  user_roles UserRole[]
  tenant     Tenant     @relation(fields: [tenant_id], references: [id])

  @@map("users")
}

model Role {
  id         String     @id @db.Uuid
  name       String?    @db.VarChar(100)
  role_type  RoleType
  created_at DateTime   @default(now()) @db.Timestamptz(6)
  updated_at DateTime   @updatedAt @db.Timestamptz(6)
  user_role  UserRole[]

  @@map("roles")
}

model UserRole {
  user_id    String   @db.VarChar(100)
  role_id    String   @db.Uuid
  created_at DateTime @default(now()) @db.Timestamptz(6)
  role       Role     @relation(fields: [role_id], references: [id])
  user       User     @relation(fields: [user_id], references: [id])

  @@id([user_id, role_id])
  @@map("user_roles")
}

model Permission {
  id               String      @id @db.Uuid
  resouce_selector Json
  resource_type    ResouceType
  code             String      @db.VarChar(100)
  created_at       DateTime    @default(now()) @db.Timestamptz(6)
  updated_at       DateTime    @updatedAt @db.Timestamptz(6)

  @@map("permissions")
}

model UserPermission {
  user_id       String   @db.VarChar(100)
  permission_id String   @db.Uuid
  created_at    DateTime @default(now()) @db.Timestamptz(6)

  @@id([user_id, permission_id])
  @@map("user_permissions")
}

model Dataset {
  id            String         @id @db.Uuid
  name          String         @db.VarChar(100)
  created_at    DateTime       @default(now()) @db.Timestamptz(6)
  updated_at    DateTime       @updatedAt @db.Timestamptz(6)
  deprecated    Boolean        @default(false)
  description   String?
  source_config Json?
  source_type   DataSourceType
  workspace_id  String         @db.Uuid
  namespace     String         @unique @default(nanoid(8)) @db.VarChar(12)
  data_entities DataEntity[]
  workspace     Workspace      @relation(fields: [workspace_id], references: [id])

  @@map("datasets")
}

model DataEntity {
  id                      String                @id @db.Uuid
  name                    String                @db.VarChar(100)
  created_at              DateTime              @default(now()) @db.Timestamptz(6)
  updated_at              DateTime              @updatedAt @db.Timestamptz(6)
  entity_type             EntityType
  ddl_script              String?
  init_script             String?
  pipeline                Json?
  dataset_id              String                @db.Uuid
  deprecated              Boolean               @default(false)
  description             String?
  structure               Json
  source_path             String?               @db.VarChar(500)
  dataset                 Dataset               @relation(fields: [dataset_id], references: [id])
  components              EntityComponent[]
  source_relationships    EntityRelationship[]  @relation("source")
  target_relationships    EntityRelationship[]  @relation("target")
  workspace_data_entities WorkspaceDataEntity[]

  @@unique([dataset_id, name])
  @@map("data_entities")
}

model EntityRelationship {
  id               String       @id @db.Uuid
  created_at       DateTime     @default(now()) @db.Timestamptz(6)
  updated_at       DateTime     @updatedAt @db.Timestamptz(6)
  related_fields   String[]
  relation_type    RelationType
  source_entity_id String       @db.Uuid
  target_entity_id String       @db.Uuid
  source_entity    DataEntity   @relation("source", fields: [source_entity_id], references: [id])
  target_entity    DataEntity   @relation("target", fields: [target_entity_id], references: [id])

  @@map("entity_relationships")
}

model Workspace {
  id                      String                @id @db.Uuid
  name                    String                @db.VarChar(100)
  description             String?
  logo                    Bytes?
  created_at              DateTime              @default(now()) @db.Timestamptz(6)
  updated_at              DateTime              @updatedAt @db.Timestamptz(6)
  apps                    App[]
  Dataset                 Dataset[]
  components              EntityComponent[]
  workspace_data_entities WorkspaceDataEntity[]

  @@map("workspaces")
}

model WorkspaceDataEntity {
  workspace_id   String     @db.Uuid
  data_entity_id String     @db.Uuid
  created_at     DateTime   @default(now()) @db.Timestamptz(6)
  data_entity    DataEntity @relation(fields: [data_entity_id], references: [id])
  workspace      Workspace  @relation(fields: [workspace_id], references: [id])

  @@id([workspace_id, data_entity_id])
  @@map("workspace_data_entities")
}

model EntityComponent {
  id             String        @id @db.Uuid
  name           String        @db.VarChar(100)
  created_at     DateTime      @default(now())
  updated_at     DateTime      @updatedAt
  component_type ComponentType
  data_entity_id String        @db.Uuid
  config         Json
  workspace_id   String        @db.Uuid
  data_entity    DataEntity    @relation(fields: [data_entity_id], references: [id])
  workspace      Workspace     @relation(fields: [workspace_id], references: [id])

  @@map("entity_components")
}

model App {
  id           String      @id @db.Uuid
  name         String      @db.VarChar(100)
  created_at   DateTime    @default(now()) @db.Timestamptz(6)
  updated_at   DateTime    @updatedAt @db.Timestamptz(6)
  config       Json
  workspace_id String      @db.Uuid
  entry        Json
  menu_items   Json
  app_domains  AppDomain[]
  workspace    Workspace   @relation(fields: [workspace_id], references: [id])

  @@map("apps")
}

model AppDomain {
  id         String   @id @db.Uuid
  name       String   @db.VarChar(100)
  created_at DateTime @default(now()) @db.Timestamptz(6)
  updated_at DateTime @updatedAt @db.Timestamptz(6)
  config     Json
  app_id     String   @db.Uuid
  tenant_id  String   @db.Uuid
  app        App      @relation(fields: [app_id], references: [id])
  tenant     Tenant   @relation(fields: [tenant_id], references: [id])

  @@map("app_domains")
}

model ExternalApi {
  id          String        @id @db.Uuid
  name        String        @db.VarChar(100)
  description String?
  base_url    String        @db.VarChar(1024)
  auth_config Json?
  created_at  DateTime      @default(now()) @db.Timestamptz(6)
  updated_at  DateTime      @updatedAt @db.Timestamptz(6)
  components  Json?
  endpoints   ApiEndpoint[]

  @@map("external_apis")
}

model ApiEndpoint {
  id              String      @id @db.Uuid
  name            String      @db.VarChar(100)
  description     String?
  external_api_id String      @db.Uuid
  method          HttpMethod
  path            String      @db.VarChar(1024)
  parameters      Json?
  request_body    Json?
  response        Json
  ingest_configs  Json?
  created_at      DateTime    @default(now()) @db.Timestamptz(6)
  updated_at      DateTime    @updatedAt @db.Timestamptz(6)
  external_api    ExternalApi @relation(fields: [external_api_id], references: [id])

  @@map("api_endpoints")
}

/// We could not retrieve columns for the underlying table. Either it has none or you are missing rights to see them. Please check your privileges.
// model workflow_jobs {
// }

/// We could not retrieve columns for the underlying table. Either it has none or you are missing rights to see them. Please check your privileges.
// model workflows {
// }

enum JobStatus {
  Pending
  Running
  Success
  Failed
  Canceled
}

enum HttpMethod {
  Get
  Post
}

enum ResouceType {
  Dataset
  Workspace
  Tenant
  AppDomain
}

enum EntityType {
  Table
  View
}

enum DataSourceType {
  Api
  Ftp
  File
  Database
  S3
  FetchFusion
  View
}

enum RelationType {
  ONE_TO_ONE
  ONE_TO_MANY
}

enum ComponentType {
  Chart
  Table
  Card
  Detail
}

enum RoleType {
  SysAdmin
  DataManager
  PublishManager
  Editor
  AppUser
}

import { useState, useRef, Key, } from 'react';
import { Modal, Input, Button, message, Tree,Tooltip } from 'antd';
import { SearchOutlined, FunctionOutlined, FieldStringOutlined } from '@ant-design/icons';

const { TextArea } = Input;

// 字段定义
const fieldTypes = {
    '数值字段': [
        { name: '字段名称1', type: '数值-整数', category: '数值' },
        { name: '字段名称2', type: '数值-小数', category: '数值' },
        { name: '字段名称3', type: '数值-整数', category: '数值' },
        { name: '销售金额', type: '数值-小数', category: '数值' },
        { name: '数量', type: '数值-整数', category: '数值' },
        { name: '单价', type: '数值-小数', category: '数值' },
        { name: '折扣率', type: '数值-小数', category: '数值' },
        { name: '成本', type: '数值-小数', category: '数值' }
    ],
    '日期字段': [
        { name: '字段名称11', type: '日期', category: '日期' },
        { name: '字段名称21', type: '日期', category: '日期' },
        { name: '字段名称31', type: '日期', category: '日期' },
        { name: '创建时间', type: '日期', category: '日期' },
        { name: '更新时间', type: '日期', category: '日期' },
        { name: '发货日期', type: '日期', category: '日期' },
        { name: '到期日期', type: '日期', category: '日期' }
    ],
    '其他字段': [
        { name: '字段名称121', type: '文本', category: '文本' },
        { name: '字段名称211', type: '图片', category: '其他' },
        { name: '字段名称321', type: '数组', category: '其他' },
        { name: '客户名称', type: '文本', category: '文本' },
        { name: '产品描述', type: '文本', category: '文本' },
        { name: '备注信息', type: '文本', category: '文本' }
    ]
};

// 函数定义 - 根据提供的函数表
const allFunctions = {
    // 数值函数
    'abs': {
        name: 'abs',
        supportedTypes: ['数值'],
        description: 'abs(x) - 绝对值',
        paramCount: 1,
        template: 'abs()'
    },
    'round': {
        name: 'round',
        supportedTypes: ['数值'],
        description: 'round(NUMERIC_s) - 精确保留小数点',
        paramCount: 1,
        template: 'round()'
    },
    'INTEGER': {
        name: 'INTEGER',
        supportedTypes: ['数值'],
        description: 'INTEGER - 精确保留小数点',
        paramCount: 1,
        template: 'INTEGER()'
    },
    'ceil': {
        name: 'ceil',
        supportedTypes: ['数值'],
        description: 'ceil(x) - 向上取整',
        paramCount: 1,
        template: 'ceil()'
    },
    'trunc': {
        name: 'trunc',
        supportedTypes: ['数值'],
        description: 'trunc(x) - 截取整数部分',
        paramCount: 1,
        template: 'trunc()'
    },
    'sum': {
        name: 'sum',
        supportedTypes: ['数值'],
        description: 'sum(x, y, ...) - 求和函数',
        paramCount: 2,
        template: 'sum(, )'
    },
    'avg': {
        name: 'avg',
        supportedTypes: ['数值'],
        description: 'avg(x, y, ...) - 平均值函数',
        paramCount: 2,
        template: 'avg(, )'
    },

    // 日期函数
    'year': {
        name: 'year',
        supportedTypes: ['日期'],
        description: 'year(x) - 获取日期年份',
        paramCount: 1,
        template: 'year()'
    },
    'yearmonth': {
        name: 'yearmonth',
        supportedTypes: ['日期'],
        description: 'yearmonth(x) - 获取日期年月，返回格式：2024-01',
        paramCount: 1,
        template: 'yearmonth()'
    },
    'yearquarter': {
        name: 'yearquarter',
        supportedTypes: ['日期'],
        description: 'yearquarter(x) - 获取日期年季度，返回格式：2024-Q1',
        paramCount: 1,
        template: 'yearquarter()'
    },
    'yearweek': {
        name: 'yearweek',
        supportedTypes: ['日期'],
        description: 'yearweek(x) - 获取日期年周，返回格式：2024-01',
        paramCount: 1,
        template: 'yearweek()'
    },
    'add_day': {
        name: 'add_day',
        supportedTypes: ['日期'],
        description: 'add_day(x) - 当前日期的基础上+天数，可以为负数',
        paramCount: 2,
        template: 'add_day(, )'
    },
    'add_month': {
        name: 'add_month',
        supportedTypes: ['日期'],
        description: 'add_month(x) - 当前日期的基础上+月数，可以为负数',
        paramCount: 2,
        template: 'add_month(, )'
    },
    'add_year': {
        name: 'add_year',
        supportedTypes: ['日期'],
        description: 'add_year(x) - 当前日期的基础上+年数，可以为负数',
        paramCount: 2,
        template: 'add_year(, )'
    }
};

// 计算字符
const operators = ['+', '-', '*', '/', '(', ')'];

interface FormulaEditorProps {
    open: boolean;
    setOpen: React.Dispatch<React.SetStateAction<boolean>>;
}

const FormulaEditor = ({ open, setOpen }:FormulaEditorProps) => {
    const [selectedFieldType, setSelectedFieldType] = useState<Key>('数值字段');
    const [formula, setFormula] = useState('');
    // const [selectedFunction, setSelectedFunction] = useState('');
    const [cursorPosition, setCursorPosition] = useState(0);
    const [fieldSearchText, setFieldSearchText] = useState('');
    const [functionSearchText, setFunctionSearchText] = useState('');
    const formulaInputRef = useRef(null);

    // 根据选择的字段类型过滤函数
    const getAvailableFunctions = () => {
        const selectedFields = Object.values(fieldTypes).flat().filter(field =>
            selectedFieldType === '数值字段' ? field.category === '数值' :
                selectedFieldType === '日期字段' ? field.category === '日期' :
                    true
        );
        if (selectedFields.length === 0) return [];
        const fieldCategories = [...new Set(selectedFields.map(field => field.category))];
        return Object.values(allFunctions).filter(func =>
            func.supportedTypes.some(type => fieldCategories.includes(type))
        );
    };

    // 过滤字段
    const getFilteredFields = () => {
        const fields = fieldTypes[selectedFieldType] || [];
        if (!fieldSearchText) return fields;
        return fields.filter(field =>
            field.name.toLowerCase().includes(fieldSearchText.toLowerCase())
        );
    };

    // 过滤函数
    const getFilteredFunctions = () => {
        const functions = getAvailableFunctions();
        if (!functionSearchText) return functions;
        return functions.filter(func =>
            func.name.toLowerCase().includes(functionSearchText.toLowerCase()) ||
            func.description.toLowerCase().includes(functionSearchText.toLowerCase())
        );
    };

    // 插入字段到公式
    const insertField = (field) => {
        const fieldToken = `[${field.name}]`;
        const newFormula = formula.slice(0, cursorPosition) + fieldToken + formula.slice(cursorPosition);
        setFormula(newFormula);
        setCursorPosition(cursorPosition + fieldToken.length);
        if (formulaInputRef.current) {
            formulaInputRef.current.focus();
        }
    };

    // 插入函数到公式
    const insertFunction = (func) => {
        const functionTemplate = func.template;
        const newFormula = formula.slice(0, cursorPosition) + functionTemplate + formula.slice(cursorPosition);
        setFormula(newFormula);
        // 计算光标位置：移动到第一个参数位置（第一个括号内）
        let newCursorPosition;
        if (func.paramCount === 1) {
            // 单参数函数：光标移动到括号内
            newCursorPosition = cursorPosition + func.name.length + 1;
        } else {
            // 多参数函数：光标移动到第一个参数位置
            newCursorPosition = cursorPosition + func.name.length + 1;
        }
        setCursorPosition(newCursorPosition);
        // setSelectedFunction(func.name);
        // 延迟设置光标位置，确保DOM更新后执行
        // setTimeout(() => {
            if (formulaInputRef.current) {
                formulaInputRef.current.focus();
                formulaInputRef.current.setSelectionRange(newCursorPosition, newCursorPosition);
            }
        // }, 0);
    };

    // 插入操作符到公式
    const insertOperator = (operator) => {
        const newFormula = formula.slice(0, cursorPosition) + operator + formula.slice(cursorPosition);
        setFormula(newFormula);
        setCursorPosition(cursorPosition + operator.length);
        if (formulaInputRef.current) {
            formulaInputRef.current.focus();
        }
    };

    // 公式校验
    const validateFormula = () => {
        if (!formula.trim()) return { valid: false, message: '公式不能为空' };

        // 检查括号匹配
        let openParens = 0;
        for (let char of formula) {
            if (char === '(') openParens++;
            if (char === ')') openParens--;
            if (openParens < 0) return { valid: false, message: '括号不匹配' };
        }
        if (openParens !== 0) return { valid: false, message: '括号不匹配' };

        // 检查函数调用格式
        const functionPattern = /(\w+)\((.*?)\)/g;
        let match;
        while ((match = functionPattern.exec(formula)) !== null) {
            const [fullMatch, funcName, params] = match;

            // 检查函数是否存在
            if (!allFunctions[funcName]) {
                return { valid: false, message: `函数 "${funcName}" 不存在` };
            }

            // 检查参数数量（简单检查，以逗号分隔）
            const func = allFunctions[funcName];
            if (func.paramCount > 1) {
                const paramCount = params.trim() ? params.split(',').length : 0;
                if (paramCount !== func.paramCount) {
                    return { valid: false, message: `函数 "${funcName}" 需要 ${func.paramCount} 个参数` };
                }
            }
        }

        return { valid: true, message: '公式格式正确' };
    };

    // 保存计算字段
    const saveField = () => {
        const validation = validateFormula();
        if (!validation.valid) {
            message.error(validation.message);
            return;
        }
        setOpen(false);
        setFormula('');
        // setSelectedFunction('');
        setFieldSearchText('');
        setFunctionSearchText('');
        message.success('计算字段添加成功');
    };

    // 处理光标位置变化
    const handleCursorChange = (e) => {
        setCursorPosition(e.target.selectionStart);
    };

    const validation = validateFormula();

    return (
        <Modal
            title="新增计算列"
            open={open}
            onCancel={() => {
                setOpen(false);
                setFormula('');
                // setSelectedFunction('');
                setFieldSearchText('');
                setFunctionSearchText('');
            }}
            width={1200}
            styles={{
                body: {
                    height:'30rem'
                }
            }}
            footer={[
                <Button key="cancel" onClick={() => setOpen(false)}>
                    取消
                </Button>,
                <Button key="submit" type="primary" onClick={saveField}>
                    确定
                </Button>,
            ]}
            className="formula-editor-modal"
        >
            <div className="flex gap-4 h-full">
                {/* 左侧字段区域 */}
                <div className="w-1/4 border-r pr-4">
                    <div className="mb-4">
                        <Input
                            placeholder="请输入搜索内容"
                            prefix={<SearchOutlined />}
                            value={fieldSearchText}
                            onChange={(e) => setFieldSearchText(e.target.value)}
                            allowClear
                        />
                    </div>
                    <h3 className="font-medium text-gray-700 mb-2 flex items-center gap-2">
                        <FieldStringOutlined className="text-blue-600" />
                        选择字段
                    </h3>
                    <Tree
                        className="mb-4 h-96 overflow-y-auto"
                        // showLine={{ showLeafIcon: false }}
                        blockNode
                        selectedKeys={[selectedFieldType]}
                        onSelect={(keys) => setSelectedFieldType(keys[0])}
                        // defaultExpandedKeys={['数值字段']}
                        titleRender={(nodeData) => {
                            // console.log(nodeData)
                            if (nodeData.isLeaf) {
                                const field = nodeData.dataRef;
                                return (
                                    <div
                                        className="p-2 cursor-pointer hover:bg-blue-50 rounded text-sm border border-transparent hover:border-blue-200 transition-all"
                                        onClick={() => insertField(field)}
                                    >
                                        <div className="font-medium text-gray-800">{field.name}</div>
                                        <div className="text-xs text-gray-500">{field.type}</div>
                                    </div>
                                );
                            }
                            return <span className="font-medium">{nodeData.title}</span>;
                        }}
                        treeData={Object.entries(fieldTypes).map(([category, fields]) => ({
                            title: category,
                            key: category,
                            children: getFilteredFields()
                                .filter(field => fieldTypes[category].includes(field))
                                .map(field => ({
                                    title: field.name,
                                    key: `${category}-${field.name}`,
                                    isLeaf: true,
                                    dataRef: field
                                }))
                        }))}
                    />
                </div>
                {/* 中间函数区域 */}
                <div className="w-1/4 border-r pr-4">
                    <div className="mb-4">
                        <Input
                            placeholder="请输入搜索内容"
                            prefix={<SearchOutlined />}
                            value={functionSearchText}
                            onChange={(e) => setFunctionSearchText(e.target.value)}
                            allowClear
                        />
                    </div>
                    <div className="mb-4">
                        <h3 className="font-medium text-gray-700 mb-2 flex items-center gap-2">
                            <FunctionOutlined className="text-blue-600" />
                            可用函数
                        </h3>
                        <div
                            className="space-y-2 overflow-y-auto h-96 pr-2"
                        >
                            {getFilteredFunctions().map((func) => (
                                <Tooltip title={func.description??''}>
                                    <div
                                        key={func.name}
                                        className="p-3 cursor-pointer hover:bg-blue-50 rounded border border-gray-200 hover:border-blue-300 transition-all"
                                        onClick={() => insertFunction(func)}
                                    >
                                        <div className="font-medium text-blue-600 text-sm">{func.name}</div>
                                        <div className="text-xs text-gray-500 mt-1">
                                            {func.supportedTypes.join(', ')} 函数
                                        </div>
                                    </div>
                                </Tooltip>
                            ))}
                            {getFilteredFunctions().length === 0 && (
                                <div className="text-gray-500 text-sm text-center py-4">
                                    没有可用的函数
                                </div>
                            )}
                        </div>
                    </div>
                </div>

                {/* 右侧编辑区域 */}
                <div className="flex-1 flex flex-col">

                    {/* 计算字符 */}
                    <div className="mb-4">
                        <label className="block text-sm font-medium mb-2">运算符：</label>
                        <div className="flex gap-2 mb-2">
                            {operators.map((op) => (
                                <Button
                                    key={op}
                                    onClick={() => insertOperator(op)}
                                    className="min-w-8"
                                >
                                    {op}
                                </Button>
                            ))}
                        </div>
                        {/* <div className="text-xs text-red-500">字段与字段之间添加运算符</div> */}
                    </div>

                    {/* 公式编辑区 */}
                    <div className="mb-4 flex-1">
                        <label className="block text-sm font-medium mb-2">公式编辑区：</label>
                        <TextArea
                            ref={formulaInputRef}
                            value={formula}
                            onChange={(e) => setFormula(e.target.value)}
                            onSelect={handleCursorChange}
                            onClick={handleCursorChange}
                            placeholder="示例：=sum(字段1+字段2)"
                            rows={4}
                            className="resize-none"
                        />
                        <div className="text-xs text-gray-500 mt-1">
                            示例：=字段1/字段2
                        </div>
                    </div>

                    {/* 公式校验结果 */}
                    <div className="mb-4">
                        <div className="flex items-center gap-2">
                            <span className="text-sm font-medium">公式校验结果：</span>
                            <span className={`text-sm px-2 py-1 rounded ${validation.valid
                                ? 'text-green-700 bg-green-100'
                                : 'text-red-700 bg-red-100'
                                }`}>
                                {validation.message}
                            </span>
                        </div>
                    </div>

                    {/* 函数说明区 */}
                    {/* <div className="mb-4">
                        <label className="block text-sm font-medium mb-2">函数说明：</label>
                        <div className="p-3 bg-gray-50 rounded border min-h-16 text-sm">
                            {}
                        </div>
                    </div> */}
                </div>
            </div>
        </Modal>
        //     </div>
        // </div>
    );
};

export default FormulaEditor;

// import { readFileSync } from 'fs';
// import * as yaml from 'js-yaml';
// import { join } from 'path';

// const YAML_CONFIG_FILENAME = 'config.yaml';

// export default () => {
//   return yaml.load(readFileSync(join(__dirname, '..', YAML_CONFIG_FILENAME), 'utf8')) as Record<string, any>;
// };

export default () => {
  return {
    DATABASE_URL: process.env.DATABASE_URL!,
    FETCHFUSION_URL: process.env.FETCHFUSION_URL!,
    WORK_DIR: process.env.WORK_DIR || '/xbuild',
  };
};

import {
	// MiniMap,
	Controls,
	ReactFlow,
	// Background,
	useNodesState,
	useEdgesState,
	// addEdge,
	// Connection,
	Edge,
	Node,
	NodeTypes,
	Panel,
	// MarkerType,
	// getSmoothStepPath,
	// getBezierPath,
	EdgeTypes,
	addEdge,
	OnConnect,
	// Handle,
	// useReactFlow,
} from '@xyflow/react';

import '@xyflow/react/dist/style.css';

import { Button } from 'antd';
import { PlusCircleOutlined } from '@ant-design/icons';
import { useCallback } from 'react';
import CustomNode from './CustomNode';
import CustomEdge from './CustomEdge';



const nodeTypes: NodeTypes = {
	'custom': CustomNode,
};

const edgeTypes: EdgeTypes = {
	'default': CustomEdge,
};
// 初始节点数据
const initialNodes: Node[] = [
	{
		id: 'voice-interaction',
		type: 'custom',
		data: {
			label: '声量互动量表',
			fields: ['字段8', '字段7']
		},
		position: { x: 500, y: 100 },
		style: { width: 180 },
		// sourcePosition: Position.Right,
		// targetPosition: Position.Left,
	},
	{
		id: 'word-cloud',
		type: 'custom',
		data: {
			label: '词云表',
			fields: ['字段1']
		},
		position: { x: 250, y: 230 },
		style: { width: 180 },
		// sourcePosition: Position.Right,
		// targetPosition: Position.Left,
	},
	{
		id: 'interaction',
		type: 'custom',
		data: {
			label: '量化互动量',
			fields: ['字段2', '字段3']
		},
		position: { x: 250, y: 320 },
		style: { width: 180 },
		// sourcePosition: Position.Right,
		// targetPosition: Position.Left,
	},
	{
		id: 'public-comparison',
		type: 'custom',
		data: {
			label: '舆情比表',
			fields: ['字段4', '字段5']
		},
		position: { x: 250, y: 430 },
		style: { width: 180 },
		// sourcePosition: Position.Right,
		// targetPosition: Position.Left,
	},
	{
		id: 'table1',
		type: 'custom',
		data: {
			label: '表1',
			fields: ['字段9']
		},
		position: { x: 750, y: 230 },
		style: { width: 180 },
		// sourcePosition: Position.Right,
		// targetPosition: Position.Left,
	},
];

// 初始边数据
const initialEdges: Edge[] = [
	{
		id: 'e1-2',
		source: 'word-cloud',
		target: 'voice-interaction',
		data: {
			startLabel: '1',
			endLabel: 'N',
		},
		type: 'default',
		sourceHandle: 'left',
		// markerEnd: {
		// 	type: MarkerType.ArrowClosed,
		// },
	},
	{
		id: 'e3-8',
		source: 'interaction',
		target: 'table1',
		data: {
			startLabel: '1',
			endLabel: 'N',
		},
		type: 'default',
		sourceHandle: 'left',
		targetHandle: 'right',
		// markerEnd: {
		// 	type: MarkerType.ArrowClosed,
		// },
	},
];

const RelationView = () => {
	const [nodes, _, onNodesChange] = useNodesState(initialNodes);
	const [edges, setEdges, onEdgesChange] = useEdgesState(initialEdges);

	const onConnect: OnConnect = useCallback(
		(params) => setEdges((eds) => addEdge(params, eds)),
		[setEdges],
	);

	return (
		<div className="flex-1" style={{ height: 'calc(100vh - 200px)' }}>
			{/* <div className='h-full flex-1 flex flex-col'> */}
			<ReactFlow
				nodes={nodes}
				edges={edges}
				onNodesChange={onNodesChange}
				onEdgesChange={onEdgesChange}
				onConnect={onConnect}
				nodeTypes={nodeTypes}
				edgeTypes={edgeTypes}
				snapToGrid={true}
				fitView
			>
				<Controls />
				{/* <MiniMap /> */}
				{/* <Background  gap={12} size={1} /> */}
				<Panel position="top-right">
					<Button type="primary" icon={<PlusCircleOutlined />}>添加关联</Button>
				</Panel>
			</ReactFlow>
			{/* </div> */}
		</div>
	);
};

export default RelationView;
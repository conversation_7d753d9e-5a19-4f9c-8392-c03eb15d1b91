import { PlusOutlined, DeleteOutlined, UpOutlined, DownOutlined } from "@ant-design/icons";
import { Button, Select, Radio, Space, Tooltip } from "antd";

// 排序条件类型定义
export type SortCondition = {
  field: string;
  direction: 'asc' | 'desc';
};

// 组件Props类型定义
interface SortOperationProps {
  sortConditions: SortCondition[];
  setSortConditions: React.Dispatch<React.SetStateAction<SortCondition[]>>;
  // fieldKeys: string[];
  getAvailableFields: (currentField: string, index: number) => string[];
};

const SortOperation = ({
  sortConditions,
  setSortConditions,
  // fieldKeys,
  getAvailableFields
}: SortOperationProps) => {
  
  // 添加新的排序条件
  const addSortCondition = () => {
    setSortConditions([...sortConditions, { field: '', direction: 'desc' }]);
  };

  // 删除排序条件
  const removeSortCondition = (index: number) => {
    setSortConditions(sortConditions.filter((_, i) => i !== index));
  };

  // 更新排序条件的字段
  const updateSortField = (index: number, field: string) => {
    const newConditions = [...sortConditions];
    newConditions[index].field = field;
    setSortConditions(newConditions);
  };

  // 更新排序条件的方向
  const updateSortDirection = (index: number, direction: 'asc' | 'desc') => {
    const newConditions = [...sortConditions];
    newConditions[index].direction = direction;
    setSortConditions(newConditions);
  };

  // 向上移动排序条件（提高优先级）
  const moveSortConditionUp = (index: number) => {
    if (index > 0) {
      const newConditions = [...sortConditions];
      [newConditions[index - 1], newConditions[index]] = [newConditions[index], newConditions[index - 1]];
      setSortConditions(newConditions);
    }
  };

  // 向下移动排序条件（降低优先级）
  const moveSortConditionDown = (index: number) => {
    if (index < sortConditions.length - 1) {
      const newConditions = [...sortConditions];
      [newConditions[index], newConditions[index + 1]] = [newConditions[index + 1], newConditions[index]];
      setSortConditions(newConditions);
    }
  };

  return (
    <div className="mb-4 p-2 rounded-md">
      <div className="flex items-center justify-between mb-3">
        <span className="font-bold">排序条件</span>
        <Button
          type="primary"
          size="small"
          icon={<PlusOutlined />}
          onClick={addSortCondition}
        >
          添加排序
        </Button>
      </div>

      {sortConditions.length === 0 ? (
        <div className="text-gray-500 text-center py-4">
          暂无排序条件，点击"添加排序"开始设置排序规则
        </div>
      ) : (
        <div className="space-y-2">
          {sortConditions.map((condition, index) => (
            <div key={index} className="flex items-center gap-2 p-2 bg-white rounded border">
              {/* 优先级指示器 */}
              <div className="flex flex-col items-center">
                <span className="text-xs text-gray-500 mb-1">优先级</span>
                <span className="text-sm font-bold text-blue-600">{index + 1}</span>
              </div>

              {/* 字段选择器 */}
              <div className="flex-1">
                <Select
                  placeholder="请选择排序字段"
                  style={{ width: '100%' }}
                  value={condition.field || undefined}
                  onChange={(value) => updateSortField(index, value)}
                  options={getAvailableFields(condition.field, index).map(key => ({
                    label: key,
                    value: key
                  }))}
                  allowClear
                />
              </div>

              {/* 排序方向选择器 */}
              {/* {condition.field && ( */}
                <div className="flex-1">
                  <Radio.Group
                    value={condition.direction}
                    onChange={(e) => updateSortDirection(index, e.target.value)}
                    options={[
                      { label: '升序', value: 'asc' },
                      { label: '降序', value: 'desc' },
                    ]}
                    optionType="button"
                    buttonStyle="solid"
                    size="small"
                  />
                </div>
              {/* )} */}

              {/* 操作按钮组 */}
              <Space>
                {/* 向上移动按钮 */}
                <Tooltip title="提高优先级">
                  <Button
                    type="text"
                    size="small"
                    icon={<UpOutlined />}
                    disabled={index === 0}
                    onClick={() => moveSortConditionUp(index)}
                  />
                </Tooltip>

                {/* 向下移动按钮 */}
                <Tooltip title="降低优先级">
                  <Button
                    type="text"
                    size="small"
                    icon={<DownOutlined />}
                    disabled={index === sortConditions.length - 1}
                    onClick={() => moveSortConditionDown(index)}
                  />
                </Tooltip>

                {/* 删除按钮 */}
                <Tooltip title="删除排序条件">
                  <Button
                    type="text"
                    size="small"
                    icon={<DeleteOutlined />}
                    onClick={() => removeSortCondition(index)}
                    danger
                  />
                </Tooltip>
              </Space>
            </div>
          ))}
        </div>
      )}

      {/* 排序说明 */}
      {sortConditions.length > 0 && (
        <div className="text-xs text-gray-500 mt-3">
          <div>• 排序优先级从上到下递减，优先级1的字段将首先排序</div>
          <div>• 可以使用上下箭头调整排序优先级</div>
          {/* <div>• 选择"不排序"将保持该字段的原始顺序</div> */}
        </div>
      )}
    </div>
  );
};

export default SortOperation;

import { PlusOutlined, DeleteOutlined, EditOutlined } from "@ant-design/icons";
import { Button, Modal, Select, Input, Form, Space, Tag } from "antd";
import { useState } from "react";

// 时间聚合条件类型定义
export type TimeAggregation = {
  id: string;
  field: string;
  aggregateFunction: 'sum' | 'count' | 'avg';
  timeGrouping?: 'year' | 'quarter' | 'month' | 'week' | 'day';
  newFieldName?: string;
};

// 组件Props类型定义
type TimeAggregationOperationProps = {
  timeAggregations: TimeAggregation[];
  setTimeAggregations: React.Dispatch<React.SetStateAction<TimeAggregation[]>>;
  fieldKeys: string[];
  isTimeField: (field: string) => boolean;
};
// 聚合函数选项
const aggregateFunctionOptions = [
  { label: '求和', value: 'sum' },
  { label: '计数', value: 'count' },
  { label: '平均值', value: 'avg' }
];

// 时间聚合方式选项
const timeGroupingOptions = [
  { label: '年', value: 'year' },
  { label: '季度', value: 'quarter' },
  { label: '月', value: 'month' },
  { label: '周', value: 'week' },
  { label: '日', value: 'day' }
];

const TimeAggregationOperation = ({
  timeAggregations,
  setTimeAggregations,
  fieldKeys,
  isTimeField
}: TimeAggregationOperationProps) => {
  
  // 弹窗状态
  const [modalOpen, setModalOpen] = useState(false);
  const [editingId, setEditingId] = useState<string | null>(null);
  
  // 表单实例
  const [form] = Form.useForm();

  // 打开添加聚合弹窗
  const openAddModal = () => {
    setEditingId(null);
    form.resetFields();
    setModalOpen(true);
  };

  // 打开编辑聚合弹窗
  const openEditModal = (aggregation: TimeAggregation) => {
    setEditingId(aggregation.id);
    form.setFieldsValue(aggregation);
    setModalOpen(true);
  };

  // 关闭弹窗
  const closeModal = () => {
    setModalOpen(false);
    setEditingId(null);
    form.resetFields();
  };

  // 保存聚合配置
  const handleSave = async () => {
    try {
      const values = await form.validateFields();
      const newAggregation: TimeAggregation = {
        id: editingId || Date.now().toString(),
        field: values.field,
        aggregateFunction: values.aggregateFunction,
        timeGrouping: values.timeGrouping,
        newFieldName: values.newFieldName
      };

      if (editingId) {
        // 编辑现有聚合
        setTimeAggregations(prev => 
          prev.map(item => item.id === editingId ? newAggregation : item)
        );
      } else {
        // 添加新聚合
        setTimeAggregations(prev => [...prev, newAggregation]);
      }

      closeModal();
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };

  // 删除聚合条件
  const removeAggregation = (id: string) => {
    setTimeAggregations(prev => prev.filter(item => item.id !== id));
  };

  // 获取聚合条件显示文本
  const getAggregationDisplayText = (aggregation: TimeAggregation) => {
    const functionText = aggregateFunctionOptions.find(opt => opt.value === aggregation.aggregateFunction)?.label;
    const timeText = aggregation.timeGrouping 
      ? timeGroupingOptions.find(opt => opt.value === aggregation.timeGrouping)?.label 
      : '';
    
    if (isTimeField(aggregation.field) && aggregation.timeGrouping) {
      return `${aggregation.field} - ${functionText} (按${timeText}聚合)`;
    }
    return `${aggregation.field} - ${functionText}`;
  };

  return (
    <div className="mb- p-2 rounded-m">
      <div className="flex items-center justify-between mb-3">
        <span className="font-bold">时间聚合</span>
        <Button
          type="primary"
          size="small"
          icon={<PlusOutlined />}
          onClick={openAddModal}
        >
          添加聚合
        </Button>
      </div>

      {timeAggregations.length === 0 ? (
        <div className="text-gray-500 text-center py-4">
          暂无聚合条件，点击"添加聚合"开始设置聚合规则
        </div>
      ) : (
        <div className="space-y-2">
          {timeAggregations.map((aggregation) => (
            <div key={aggregation.id} className="flex items-center justify-between p-2 bg-white rounded border">
              <div className="flex-1">
                <Tag color="blue" className="cursor-pointer" onDoubleClick={() => openEditModal(aggregation)}>
                  {getAggregationDisplayText(aggregation)}
                </Tag>
                {aggregation.newFieldName && (
                  <Tag color="green" className="ml-2">
                    新字段: {aggregation.newFieldName}
                  </Tag>
                )}
              </div>
              <Space>
                <Button
                  type="text"
                  size="small"
                  icon={<EditOutlined />}
                  onClick={() => openEditModal(aggregation)}
                  title="编辑聚合条件"
                />
                <Button
                  type="text"
                  size="small"
                  icon={<DeleteOutlined />}
                  onClick={() => removeAggregation(aggregation.id)}
                  danger
                  title="删除聚合条件"
                />
              </Space>
            </div>
          ))}
        </div>
      )}

      {/* 聚合说明 */}
      {timeAggregations.length > 0 && (
        <div className="text-xs text-gray-500 mt-3">
          <div>• 双击聚合条件可以编辑</div>
          <div>• 时间字段支持按年、季度、月、周、日进行聚合</div>
          <div>• 可以为时间聚合指定新的字段名称</div>
        </div>
      )}

      {/* 聚合配置弹窗 */}
      <Modal
        title={editingId ? "编辑聚合" : "添加聚合"}
        open={modalOpen}
        onCancel={closeModal}
        onOk={handleSave}
        okText="确定"
        cancelText="取消"
        width={500}
      >
        <Form
          form={form}
          layout="vertical"
          initialValues={{
            aggregateFunction: 'sum'
          }}
        >
          <Form.Item
            label="聚合字段"
            name="field"
            rules={[{ required: true, message: '请选择聚合字段' }]}
          >
            <Select
              placeholder="请选择字段"
              options={fieldKeys.map(key => ({
                label: key,
                value: key
              }))}
            />
          </Form.Item>

          <Form.Item
            label="聚合函数"
            name="aggregateFunction"
            rules={[{ required: true, message: '请选择聚合函数' }]}
          >
            <Select
              placeholder="请选择聚合函数"
              options={aggregateFunctionOptions}
            />
          </Form.Item>

          <Form.Item shouldUpdate={(prevValues, currentValues) => prevValues.field !== currentValues.field}>
            {({ getFieldValue }) => {
              const selectedField = getFieldValue('field');
              const isTimeFieldSelected = selectedField && isTimeField(selectedField);
              
              return isTimeFieldSelected ? (
                <>
                  <Form.Item
                    label="聚合方式"
                    name="timeGrouping"
                    rules={[{ required: true, message: '请选择聚合方式' }]}
                  >
                    <Select
                      placeholder="请选择聚合方式"
                      options={timeGroupingOptions}
                    />
                  </Form.Item>

                  <Form.Item
                    label="新增字段"
                    name="newFieldName"
                    rules={[{ required: true, message: '请输入新字段名称' }]}
                  >
                    <Input placeholder="根据聚合方式新增列的字段名称" />
                  </Form.Item>
                </>
              ) : null;
            }}
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default TimeAggregationOperation;

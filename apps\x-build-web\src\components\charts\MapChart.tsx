import type { MapChartProps } from '@web/src/types'
import { message } from 'antd'
import { getMap } from 'echarts'
import { forwardRef, useMemo } from 'react'
import { findTagByPartialTagInTrees, mapMetricToText, resolveField } from '@web/src/utils'
import EChart from './EChart'

function MapChart({
    tagDims,
    data,
    metrics,
    tags,
    chartId,
    path,
    paths,
    setPath,
    drillDown,
}: MapChartProps, ref: any) {
    const [messageApi, contextHolder] = message.useMessage();
    const options = useMemo(() => ({
        tooltip: {
            trigger: 'item',
            formatter: (params: any) => `${resolveField(params.name).tag}<br/>${mapMetricToText(metrics[0])}：${params.value}`,
        },
        // title: {
        //     text: name || '',
        // },
        // toolbox: {
        //     // show:true,
        //     itemSize: 20,
        //     orient: 'vertical',
        //     left: 'center',
        //     feature: {
        //         myTool1: {
        //             show: true,
        //             title: '缩放平移',
        //             icon: 'path://M432.45,595.444c0,2.177-4.661,6.82-11.305,6.82c-6.475,0-11.306-4.567-11.306-6.82s4.852-6.812,11.306-6.812C427.841,588.632,432.452,593.191,432.45,595.444L432.45,595.444z M421.155,589.876c-3.009,0-5.448,2.495-5.448,5.572s2.439,5.572,5.448,5.572c3.01,0,5.449-2.495,5.449-5.572C426.604,592.371,424.165,589.876,421.155,589.876L421.155,589.876z M421.146,591.891c-1.916,0-3.47,1.589-3.47,3.549c0,1.959,1.554,3.548,3.47,3.548s3.469-1.589,3.469-3.548C424.614,593.479,423.062,591.891,421.146,591.891L421.146,591.891zM421.146,591.891',
        //             onclick: () => {
        //                 options.series[0].roam = !options.series[0].roam
        //             }
        //         }
        //     }
        // },
        visualMap: {
            min: 0,
            max: 50000,
            text: ['High', 'Low'],
            realtime: false,
            calculable: true,
            inRange: {
                color: ['lightskyblue', 'yellow', 'orangered'],
            },
        },
        series: [
            {
                type: 'map',
                map: paths && paths.find((item: any) => item.id === chartId).path.length
                    ? `${chartId}-${paths.find((item: any) => item.id === chartId).path.slice(-1)}`
                    : path ? path.length > 0 ? `china-${path.slice(-1)}` : `china` : `${chartId}`,
                label: {
                    show: true,
                    formatter: (params: any) => resolveField(params.name).tag,
                },
                // zoom: 1, // 当前视角的缩放比例
                roam: true, // 是否开启平游或缩放
                scaleLimit: {
                    min: 1,
                    max: 2,
                },
                data: tags.map((tag, index) => ({
                    name: resolveField(tag).tag,
                    value: data[0][metrics[0]][index],
                })),
                universalTransition: {
                    enabled: true,
                },
                // nameMap: Object.fromEntries(tags.map((tag) => [tag, tag]))
            },
        ],
    }), [tags, data, metrics, chartId, path, paths])
    const onclick = (params: any) => {
        if (!findTagByPartialTagInTrees(tagDims, params.name)) {
            messageApi.error('无法下钻')
            return
            // console.log(findTagByPartialTagInTrees(tagDims, params.name));
        }
        const name = findTagByPartialTagInTrees(tagDims, params.name)
        chartId && drillDown && drillDown(chartId, name, tagDims, 'map')
        path && setPath && setPath([...path, name])
    }
    if (metrics.length === 0) {
        // messageApi.error('请选择指标')
        return <div>请选择指标</div>
    }
    if (!data.length) {
        return <div>暂无数据</div>
    }
    if (drillDown && !getMap(`${chartId}`)) {
        return <div>无法加载地图数据</div>
    }
    return (
        <>
            {contextHolder}
            <EChart
                options={options}
                onClick={onclick}
                ref={ref}
            />
        </>
    )
}
export default forwardRef(MapChart)

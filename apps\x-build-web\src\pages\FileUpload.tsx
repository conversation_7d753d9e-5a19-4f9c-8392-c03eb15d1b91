import { useState, useEffect, useCallback, useMemo } from 'react';
import {
	Button,
	Upload,
	Table,
	Input,
	Select,
	Checkbox,
	Modal,
	Form,
	message,
	Card,
	Tooltip,
	Space,
	Progress
} from 'antd';
import {
	UploadOutlined,
	DeleteOutlined,
	EditOutlined,
	EyeOutlined,
	// CheckCircleOutlined,
	ExclamationCircleOutlined,
	LoadingOutlined,
	CloseOutlined
} from '@ant-design/icons';
// 
import { useNavigate } from 'react-router-dom';

// Worker消息类型定义
interface ParseRequest {
	type: 'PARSE_CSV';
	fileId: string;
	file: File;
	options?: {
		preview?: number;
		encoding?: string;
	};
}

interface ParseResponse {
	type: 'PARSE_SUCCESS' | 'PARSE_ERROR' | 'PARSE_PROGRESS';
	fileId: string;
	data?: {
		fileName: string;
		fields: FieldInfo[];
		data: Record<string, any>[];
		rowCount: number;
		warnings?: any[];
	};
	error?: string;
	progress?: number;
}

// 数据类型定义
interface FieldInfo {
	name: string;
	type: 'text' | 'date' | 'integer' | 'decimal' | 'image' | 'array' | 'list';
	description?: string;
	originalName: string;
}

interface TableInfo {
	id: string;
	fileName: string;
	alias: string;
	file: File;
	fields: FieldInfo[];
	data: any[];
	selected: boolean;
	uploadStatus: 'pending' | 'uploading' | 'success' | 'error';
}

const fieldTypeOptions = [
	{ label: '文本', value: 'text' },
	{ label: '日期', value: 'date' },
	{ label: '数值-整数', value: 'integer' },
	{ label: '数值-小数', value: 'decimal' },
	{ label: '图片', value: 'image' },
	{ label: '数组', value: 'array' },
	{ label: '列表', value: 'list' },
];

// 表结构编辑器组件
interface TableStructureEditorProps {
	table: TableInfo;
	onFieldUpdate: (tableId: string, fieldIndex: number, field: string, value: any) => void;
}

const TableStructureEditor = ({ table, onFieldUpdate }: TableStructureEditorProps) => {
	// 缓存表格数据源
	const dataSource = useMemo(() => {
		return table.fields.map((field, index) => ({
			...field,
			key: index
		}));
	}, [table.fields]);

	// 缓存字段名更新回调
	const handleFieldNameChange = useCallback((index: number, value: string) => {
		onFieldUpdate(table.id, index, 'name', value);
	}, [table.id, onFieldUpdate]);

	// 缓存字段类型更新回调
	const handleFieldTypeChange = useCallback((index: number, value: string) => {
		onFieldUpdate(table.id, index, 'type', value);
	}, [table.id, onFieldUpdate]);

	// 缓存字段描述更新回调
	const handleFieldDescriptionChange = useCallback((index: number, value: string) => {
		onFieldUpdate(table.id, index, 'description', value);
	}, [table.id, onFieldUpdate]);

	// 缓存表格列定义
	const columns = useMemo(() => [
		{
			title: '字段名',
			dataIndex: 'name',
			key: 'name',
			width: 80,
			ellipsis: true,
			render: (text: string, _: any, index: number) => (
				<Input
					value={text}
					onChange={(e) => handleFieldNameChange(index, e.target.value)}
					placeholder="字段名"
					size="small"
				/>
			)
		},
		{
			title: '字段类型',
			dataIndex: 'type',
			key: 'type',
			width: 100,
			// ellipsis: true,
			render: (text: string, _: any, index: number) => (
				<Select
					value={text}
					onChange={(value) => handleFieldTypeChange(index, value)}
					options={fieldTypeOptions}
					className="w-full"
					size="small"
				/>
			)
		},
		{
			title: '字段说明',
			dataIndex: 'description',
			key: 'description',
			width: 80,
			render: (text: string, _: any, index: number) => (
				<Input
					value={text}
					onChange={(e) => handleFieldDescriptionChange(index, e.target.value)}
					placeholder="字段说明"
					size="small"
				/>
			)
		},
		{
			title: '原始字段名',
			dataIndex: 'originalName',
			key: 'originalName',
			width: 80,
			ellipsis: true,
			render: (text: string) => (
				<span className="text-gray-500 text-xs">{text}</span>
			)
		}
	], [handleFieldNameChange, handleFieldTypeChange, handleFieldDescriptionChange]);

	return (
		<div className="w-1/3 bg-white">
			<div className="p-3">
				<h3 className="text-sm font-medium text-gray-700">表结构</h3>
			</div>
			<Table
				dataSource={dataSource}
				columns={columns}
				pagination={false}
				size="small"
				scroll={{ y: 'calc(100vh - 300px)' }}
			/>
		</div>
	);
};

// 数据预览组件
interface DataPreviewProps {
	table: TableInfo;
}

const DataPreview = ({ table }: DataPreviewProps) => {
	// 缓存表格列定义
	const columns = useMemo(() => {
		return table.fields.map(field => ({
			title: field.name,
			dataIndex: field.originalName,
			key: field.originalName,
			width: 60,
			ellipsis: true,
			render: (text: any) => (
				<span className="text-xs">{String(text)}</span>
			)
		}));
	}, [table.fields]);

	return (
		<div className="w-2/3 border-l bg-white">
			<div className="p-3">
				<h3 className="text-sm font-medium text-gray-700">数据预览</h3>
			</div>
			<Table
				dataSource={table.data}
				columns={columns}
				pagination={false}
				size="small"
				scroll={{ x: 'max-content', y: 'calc(100vh - 300px)' }}
			/>
		</div>
	);
};

// 文件列表组件
interface FileListProps {
	tables: TableInfo[];
	selectedTableId: string;
	uploadingFiles: Set<string>;
	fileProgress: Map<string, number>;
	onTableSelect: (tableId: string) => void;
	onTableToggle: (tableId: string, selected: boolean) => void;
	onDeleteTable: (tableId: string) => void;
	onCancelParse: (fileId: string) => void;
	onEditAlias: (table: TableInfo) => void;
	onSetPrimaryKey: (table: TableInfo) => void;
}

const FileList = ({
	tables,
	selectedTableId,
	uploadingFiles,
	fileProgress,
	onTableSelect,
	onTableToggle,
	onDeleteTable,
	onCancelParse,
	onEditAlias,
	onSetPrimaryKey
}: FileListProps) => {
	// 缓存表格选择回调
	const handleTableClick = useCallback((tableId: string) => {
		onTableSelect(tableId);
	}, [onTableSelect]);

	// 缓存表格切换回调
	const handleTableToggle = useCallback((tableId: string, checked: boolean, e: any) => {
		e.stopPropagation();
		onTableToggle(tableId, checked);
	}, [onTableToggle]);

	return (
		<div className="flex-1 overflow-y-auto p-2 space-y-2">
			{/* 正在上传的文件 */}
			{Array.from(uploadingFiles).map(fileId => (
				<Card
					key={fileId}
					size="small"
					className="border-orange-200 bg-orange-50"
				>
					<div className="flex items-center justify-between">
						<div className="flex-1 min-w-0">
							<div className="text-sm font-medium text-orange-800 truncate">
								{fileId.split('_')[0]}
							</div>
							<div className="text-xs text-orange-600">
								解析中... {Math.round((fileProgress.get(fileId) || 0))}%
							</div>
							<Progress
								percent={Math.round((fileProgress.get(fileId) || 0))}
								size="small"
								status="active"
								className="mt-1"
							/>
						</div>
						<Button
							type="text"
							size="small"
							icon={<CloseOutlined />}
							onClick={() => onCancelParse(fileId)}
							className="text-orange-600 hover:text-orange-800"
						/>
					</div>
				</Card>
			))}

			{/* 已解析的文件列表 */}
			{tables.map(table => (
				<Card
					key={table.id}
					size="small"
					className={`cursor-pointer transition-colors ${selectedTableId === table.id
						? 'border-blue-500 bg-blue-50'
						: 'hover:border-gray-300'
						}`}
					onClick={() => handleTableClick(table.id)}
				>
					<div className="flex items-center justify-between">
						<div className="flex-1 min-w-0">
							<div className="flex items-center space-x-2">
								<Checkbox
									checked={table.selected}
									onChange={(e) => handleTableToggle(table.id, e.target.checked, e)}
								/>
								<div className="flex-1 min-w-0">
									<div className="text-sm font-medium truncate">{table.alias}</div>
									<div className="text-xs text-gray-500 truncate">{table.fileName}</div>
									<div className="text-xs text-gray-400">
										{table.fields.length} 字段 · {table.data.length} 行
									</div>
								</div>
							</div>
						</div>
						<div className="flex space-x-1">
							<Tooltip title="编辑别名">
								<Button
									type="text"
									size="small"
									icon={<EditOutlined />}
									onClick={(e) => {
										e.stopPropagation();
										onEditAlias(table);
									}}
								/>
							</Tooltip>
							<Tooltip title="设置主键">
								<Button
									type="text"
									size="small"
									icon={<EyeOutlined />}
									onClick={(e) => {
										e.stopPropagation();
										onSetPrimaryKey(table);
									}}
								/>
							</Tooltip>
							<Tooltip title="删除">
								<Button
									type="text"
									size="small"
									icon={<DeleteOutlined />}
									onClick={(e) => {
										e.stopPropagation();
										onDeleteTable(table.id);
									}}
									className="text-red-500 hover:text-red-700"
								/>
							</Tooltip>
						</div>
					</div>
				</Card>
			))}

			{/* 空状态 */}
			{tables.length === 0 && uploadingFiles.size === 0 && (
				<div className="text-center py-8 text-gray-400">
					<UploadOutlined className="text-3xl mb-2" />
					<div className="text-sm">暂无上传文件</div>
					<div className="text-xs">请点击上方按钮上传CSV文件</div>
				</div>
			)}
		</div>
	);
};

const FileUpload = () => {
	const navigate = useNavigate();
	const [tables, setTables] = useState<TableInfo[]>([]);
	const [selectedTableId, setSelectedTableId] = useState<string>('');
	const [aliasModalOpen, setAliasModalOpen] = useState(false);
	const [primaryKeyModalOpen, setPrimaryKeyModalOpen] = useState(false);
	const [currentEditTable, setCurrentEditTable] = useState<TableInfo | null>(null);
	const [uploading, setUploading] = useState(false);
	const [uploadingFiles, setUploadingFiles] = useState<Set<string>>(new Set());
	const [fileProgress, setFileProgress] = useState<Map<string, number>>(new Map());
	const [worker, setWorker] = useState<Worker | null>(null);
	const [fileMap, setFileMap] = useState<Map<string, File>>(new Map());
	const [aliasForm] = Form.useForm();

	// 初始化Worker
	useEffect(() => {
		const csvWorker = new Worker(
			new URL('../workers/csvParser.worker.ts', import.meta.url),
			{ type: 'module' }
		);
		csvWorker.onmessage = (event: MessageEvent<ParseResponse>) => {
			const { type, fileId, data, error, progress } = event.data;
			// console.log(data)
			switch (type) {
				case 'PARSE_PROGRESS':
					if (typeof progress === 'number') {
						setFileProgress(prev => new Map(prev).set(fileId, progress));
					}
					break;
				case 'PARSE_SUCCESS':
					if (data) {
						// 显示警告信息（如果有）
						if (data.warnings && data.warnings.length > 0) {
							message.warning(`解析完成，但有 ${data.warnings.length} 个警告，请检查数据完整性`);
						}

						// 创建表信息
						const file = fileMap.get(fileId);
						// console.log(file)
						const newTable: TableInfo = {
							id: Date.now().toString(),
							fileName: data.fileName,
							alias: data.fileName.replace('.csv', ''),
							file: file!,
							fields: data.fields,
							data: data.data,
							selected: true,
							uploadStatus: 'pending'
						};
						setTables(prev => [...prev, newTable]);
						setSelectedTableId(newTable.id);
						message.success(`文件 ${data.fileName} 解析成功，共 ${data.rowCount} 行数据`);
					}

					// 清除加载状态
					setUploadingFiles(prev => {
						const newSet = new Set(prev);
						newSet.delete(fileId);
						if (newSet.size === 0) {
							setUploading(false);
						}
						return newSet;
					});
					setFileProgress(prev => {
						const newMap = new Map(prev);
						newMap.delete(fileId);
						return newMap;
					});
					setFileMap(prev => {
						const newMap = new Map(prev);
						newMap.delete(fileId);
						return newMap;
					});

					break;

				case 'PARSE_ERROR':
					if (error) {
						message.error(`文件解析失败: ${error}`);
					}

					// 清除加载状态
					setUploadingFiles(prev => {
						const newSet = new Set(prev);
						newSet.delete(fileId);
						if (newSet.size === 0) {
							setUploading(false);
						}
						return newSet;
					});
					setFileProgress(prev => {
						const newMap = new Map(prev);
						newMap.delete(fileId);
						return newMap;
					});
					setFileMap(prev => {
						const newMap = new Map(prev);
						newMap.delete(fileId);
						return newMap;
					});

					break;
			}
		}
		csvWorker.onmessageerror = (error) => {
			console.error('CSV Worker error:', error);
			message.error('文件解析器出错，请刷新页面重试');
		};
		setWorker(csvWorker);

		// 清理函数
		return () => {
			csvWorker.terminate();
		};
	}, []);

	// 组件卸载时清理
	useEffect(() => {
		return () => {
			if (worker) {
				worker.terminate();
			}
		};
	}, [worker]);

	// 文件上传前处理
	const handleBeforeUpload = (file: File) => {
		const uploadFile = file;
		const fileId = `${uploadFile.name}_${Date.now()}`;

		if (!uploadFile.name.toLowerCase().endsWith('.csv')) {
			message.error('只支持CSV文件格式');
			return false;
		}

		if (uploadFile.size > 10 * 1024 * 1024) {
			message.error('文件大小不能超过10MB');
			return false;
		}

		if (!worker) {
			message.error('文件解析器未就绪，请稍后重试');
			return false;
		}

		// 设置加载状态
		setUploading(true);
		setUploadingFiles(prev => new Set([...prev, fileId]));
		setFileProgress(prev => new Map(prev).set(fileId, 0));
		setFileMap(prev => new Map(prev).set(fileId, uploadFile));

		// 发送解析请求到Worker
		const request: ParseRequest = {
			type: 'PARSE_CSV',
			fileId,
			file: uploadFile,
			options: {
				preview: 100,
				encoding: 'UTF-8'
			}
		};
		worker.postMessage(request);
		// 返回false阻止默认上传行为
		return false;
	};

	// 删除表 - 使用useCallback优化
	const handleDeleteTable = useCallback((tableId: string) => {
		setTables(prev => prev.filter(t => t.id !== tableId));
		if (selectedTableId === tableId) {
			setSelectedTableId('');
		}
	}, [selectedTableId]);

	// 取消文件解析 - 使用useCallback优化
	const handleCancelParse = useCallback((fileId: string) => {
		setUploadingFiles(prev => {
			const newSet = new Set(prev);
			newSet.delete(fileId);
			// 如果没有其他文件在解析，清除全局loading状态
			if (newSet.size === 0) {
				setUploading(false);
			}
			return newSet;
		});
		setFileProgress(prev => {
			const newMap = new Map(prev);
			newMap.delete(fileId);
			return newMap;
		});
		setFileMap(prev => {
			const newMap = new Map(prev);
			newMap.delete(fileId);
			return newMap;
		});

		message.info('已取消文件解析');
	}, []);

	// 编辑表别名 - 使用useCallback优化
	const handleEditAlias = useCallback((table: TableInfo) => {
		setCurrentEditTable(table);
		aliasForm.setFieldsValue({ alias: table.alias });
		setAliasModalOpen(true);
	}, [aliasForm]);

	// 保存别名 - 使用useCallback优化
	const handleSaveAlias = useCallback(() => {
		aliasForm.validateFields().then(values => {
			if (currentEditTable) {
				setTables(prev => prev.map(t =>
					t.id === currentEditTable.id
						? { ...t, alias: values.alias }
						: t
				));
				setAliasModalOpen(false);
				setCurrentEditTable(null);
				aliasForm.resetFields();
				message.success('别名修改成功');
			}
		});
	}, [aliasForm, currentEditTable]);

	// 字段更新回调 - 使用useCallback优化
	const handleFieldUpdate = useCallback((tableId: string, fieldIndex: number, field: string, value: any) => {
		setTables(prev => prev.map(t => {
			if (t.id === tableId) {
				const newFields = [...t.fields];
				(newFields[fieldIndex] as any)[field] = value;
				return { ...t, fields: newFields };
			}
			return t;
		}));
	}, []);

	// 表格选择回调 - 使用useCallback优化
	const handleTableSelect = useCallback((tableId: string) => {
		setSelectedTableId(tableId);
	}, []);

	// 表格切换回调 - 使用useCallback优化
	const handleTableToggle = useCallback((tableId: string, selected: boolean) => {
		setTables(prev => prev.map(t =>
			t.id === tableId
				? { ...t, selected }
				: t
		));
	}, []);

	// 设置主键回调 - 使用useCallback优化
	const handleSetPrimaryKey = useCallback((table: TableInfo) => {
		setCurrentEditTable(table);
		setPrimaryKeyModalOpen(true);
	}, []);

	// 获取当前选中的表 - 使用useMemo缓存
	const selectedTable = useMemo(() => {
		return tables.find(t => t.id === selectedTableId);
	}, [tables, selectedTableId]);

	return (
		<div className="h-[calc(100vh-64px)] flex overflow-hidden">
			{/* 左侧上传列表 */}
			<div className="w-64  border-r flex flex-col">
				{/* 顶部上传区域 */}
				<div className="p-4 border-b border-gray-100">
					<Upload
						beforeUpload={handleBeforeUpload}
						showUploadList={false}
						accept=".csv"
						multiple
					>
						<Button
							icon={uploading ? <LoadingOutlined /> : <UploadOutlined />}
							className="w-full"
							loading={uploading}
							disabled={uploading}
						>
							{uploading ? '解析中...' : '上传CSV文件'}
						</Button>
					</Upload>
				</div>

				{/* 文件列表 */}
				<FileList
					tables={tables}
					selectedTableId={selectedTableId}
					uploadingFiles={uploadingFiles}
					fileProgress={fileProgress}
					onTableSelect={handleTableSelect}
					onTableToggle={handleTableToggle}
					onDeleteTable={handleDeleteTable}
					onCancelParse={handleCancelParse}
					onEditAlias={handleEditAlias}
					onSetPrimaryKey={handleSetPrimaryKey}
				/>

				{/* 底部操作按钮 */}
				<div className="p-4 border-t border-gray-100 space-y-2">
					<Button
						type="primary"
						className="w-full"
						disabled={!tables.some(t => t.selected) || uploading}
						loading={uploading}
						onClick={() => setPrimaryKeyModalOpen(true)}
					>
						{uploading ? '解析中...' : `确定上传 (${tables.filter(t => t.selected).length})`}
					</Button>
					<Button
						className="w-full"
						onClick={() => navigate(-1)}
					>
						返回
					</Button>
				</div>
			</div>

			{/* 右侧预览区域 */}
			<div className="flex-1 flex flex-col">
				{selectedTable ? (
					<>
						{/* 顶部标题 */}
						<div className="bg-white p-4 border-b border-gray-200">
							<div className="flex items-center justify-between">
								<Space>
									<div className="text-lg font-semibold">{selectedTable.alias}</div>
									<div className="text-sm text-gray-500">{selectedTable.fileName}</div>
								</Space>
							</div>
						</div>
						{/* 内容区域 - 左右两栏布局 */}
						<div className="flex-1 flex p-2">
							<TableStructureEditor
								table={selectedTable}
								onFieldUpdate={handleFieldUpdate}
							/>
							<DataPreview table={selectedTable} />
						</div>
					</>
				) : (
					<div className="flex-1 flex items-center justify-center">
						<div className="text-center text-gray-400">
							<EyeOutlined className="text-4xl mb-4" />
							<div className="text-lg mb-2">选择文件查看预览</div>
							<div className="text-sm">请从左侧选择已上传的CSV文件</div>
						</div>
					</div>
				)}
			</div>

			{/* 编辑别名弹窗 */}
			<Modal
				title="编辑表别名"
				open={aliasModalOpen}
				onOk={handleSaveAlias}
				onCancel={() => {
					setAliasModalOpen(false);
					setCurrentEditTable(null);
					aliasForm.resetFields();
				}}
				okText="确定"
				cancelText="取消"
			>
				<Form form={aliasForm} layout="vertical">
					<Form.Item
						label="表别名"
						name="alias"
						rules={[{ required: true, message: '请输入表别名' }]}
					>
						<Input placeholder="请输入表别名" />
					</Form.Item>
				</Form>
			</Modal >

			{/* 主键设置确认弹窗 */}
			< Modal
				title="上传确认"
				open={primaryKeyModalOpen}
				onOk={() => {
					// 处理上传逻辑
					setPrimaryKeyModalOpen(false);
					message.success('上传成功');
					navigate(-1);
				}}
				onCancel={() => setPrimaryKeyModalOpen(false)}
				okText="确定"
				cancelText="取消"
			>
				<div className="space-y-4">
					<div className="flex items-center space-x-2">
						<ExclamationCircleOutlined className="text-orange-500" />
						<span>是否需要为上传的表自动设置主键？</span>
					</div>
					<div className="text-sm text-gray-500">
						系统将为每个表自动添加一个自增主键字段，用于唯一标识每条记录。
					</div>
				</div>
			</Modal >
		</div >
	);
};

export default FileUpload;

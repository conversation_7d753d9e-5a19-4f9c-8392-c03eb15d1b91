import { z } from 'zod/v3';
import { Mutation, Router, Input } from 'nestjs-trpc';
import { nanoid } from 'nanoid';
import { S3Service } from './s3.service';

const uploadSchema = z.object({ fileName: z.string(), fileType: z.string() });

@Router({ alias: 'upload' })
export class UploadRouter {
  constructor(private readonly s3Service: S3Service) {}
  @Mutation({ input: uploadSchema })
  async getPresignedUrl(@Input() input: z.infer<typeof uploadSchema>) {
    // const { nanoid } = await import('nanoid');
    const key = `uploads/${nanoid()}-${input.fileName}`;
    return await this.s3Service.getPresignedUrl(key, input.fileType);
  }
}

import { HttpModule } from '@nestjs/axios';
import { Module } from '@nestjs/common';
import { ExternalApiService } from './external-api.service';
import { AppConfigModule } from '@server/config/config.module';
import { PrismaService } from '@server/common/prisma.service';

@Module({
  imports: [AppConfigModule, HttpModule],
  providers: [ExternalApiService, PrismaService],
  exports: [ExternalApiService],
})
export class ExternalApiModule {}

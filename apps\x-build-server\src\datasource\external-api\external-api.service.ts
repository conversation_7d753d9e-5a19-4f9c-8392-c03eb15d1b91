import { HttpService } from '@nestjs/axios';
import { Injectable, Logger } from '@nestjs/common';
import { URL } from 'url';
import { v7 as uuidv7 } from 'uuid';
import { OpenAPIV3 } from 'openapi-types';
import { convertToNdjsonStream, joinUrl, saveStreamToFile } from '@server/common/utils';
// import { PrismaClient } from '@prisma/client';
import { PrismaService } from '@server/common/prisma.service';
import { EntityType, Prisma } from '@prisma/client';
import { generateDDL, mapPrimitiveType } from '@server/datasource/datasource.helper';
import { CommonColumns, PublicWorkspaceId } from '@server/common/constants';
import { EntityStructure } from '../datasource.types';
import { DuckDBInstance } from '@duckdb/node-api';
import { AppConfigService } from '@server/config/config.service';
import { AxiosRequestConfig } from 'axios';
import { JsonObject } from '@prisma/client/runtime/library';
import { JSONPath } from '@astronautlabs/jsonpath';
import * as fs from 'fs';

export type SyncOptions = {
  overwright: boolean;
};

type IngestConfig = {
  jsonPath: string;
  dataEntityId: string;
};

@Injectable()
export class ExternalApiService {
  private readonly logger = new Logger(ExternalApiService.name, { timestamp: true });
  constructor(
    private readonly httpService: HttpService,
    private readonly configService: AppConfigService,
    private readonly prisma: PrismaService,
  ) {}

  async syncFromFetchFusion(options: SyncOptions = { overwright: true }) {
    const catelog = new URL('openapi', this.configService.fetchFusionUrl);
    const res = await this.httpService.axiosRef.get(catelog.href);
    const data = res.data as OpenAPIV3.Document;
    if (!data.servers) {
      return;
    }
    for (const serverObject of data.servers) {
      this.logger.log(`获取${serverObject.description}服务的接口目录`);
      const url = new URL(serverObject.url, catelog);
      console.log(url.href);
      const res = await this.httpService.axiosRef.get(url.href);
      const doc = res.data as OpenAPIV3.Document;
      this.logger.log(`开始同步${doc.info.title}-${doc.info.description}`);
      const trans = [];

      const { datasetId, namespace } = await this.upsertDatasetByName(doc);
      const { registerExternalApi, externalApiId } = await this.upsertExternalApiByName(doc, url);
      const endpoints = registerExternalApi?.endpoints;
      for (const [key, value] of Object.entries(doc.paths)) {
        const endpointUrl = joinUrl(url.href, key);
        console.log(endpointUrl);
        // 使用Path映射endpoint和Api的关系
        const endpoint = endpoints?.find((e) => e.path === key);
        if (endpoint && !options.overwright) {
          this.logger.warn(`跳过相同的终结点定义 ${endpoint.name} 的映射`);
          continue;
        }
        const endpointId = endpoint?.id || uuidv7();
        let responseObject: OpenAPIV3.ResponseObject | null = null;
        if (value?.post) {
          const requestBody = value.post.requestBody as OpenAPIV3.RequestBodyObject;
          const responses = value.post.responses;
          if (!responses['200']) {
            continue;
          }
          responseObject = responses['200'] as OpenAPIV3.ResponseObject;
          const data: any = {
            name: value.summary || key, //使用summary作为名称
            path: key,
            method: 'Post',
            parameters: value.post.parameters as unknown as Prisma.JsonArray,
            request_body: requestBody.content as Prisma.JsonObject,
            external_api_id: externalApiId,
            response: responseObject.content as Prisma.JsonObject,
          };
          trans.push(
            this.prisma.apiEndpoint.upsert({
              where: {
                id: endpointId,
              },
              create: {
                id: endpointId,
                ...data,
              },
              update: data,
            }),
          );
        }
        if (value?.get) {
          const responses = value.get.responses;
          if (!responses['200']) {
            continue;
          }
          responseObject = responses['200'] as OpenAPIV3.ResponseObject;
          const reponseContent = responseObject.content;
          const data: any = {
            method: 'Get',
            name: value.summary || key,
            path: key,
            parameters: value.get.parameters as unknown as Prisma.JsonArray,
            external_api_id: externalApiId,
            response: reponseContent as Prisma.JsonObject,
          };
          trans.push(
            this.prisma.apiEndpoint.upsert({
              where: {
                id: endpointId,
              },
              create: {
                id: endpointId,
                ...data,
              },
              update: data,
            }),
          );
        }
        if (!responseObject) {
          throw new Error('No response schema found');
        }
        const dataEntities = extractEntityInfo(responseObject);
        if (endpoint?.ingest_configs) {
          const config = endpoint.ingest_configs as IngestConfig[];
          // this.logger.warn(`需手动处理API节点${endpoint.id}的映射关系`);
          this.logger.warn(`覆盖API节点"${endpoint.id}"到数据实体的映射`);
          const newConfigs: IngestConfig[] = [];
          // 通过ingestConfig的entityId查找对应的entity信息
          const currentEntities = await this.prisma.dataEntity.findMany({
            where: {
              id: {
                in: config.map((c) => c.dataEntityId),
              },
            },
            select: {
              id: true,
              name: true,
            },
          });
          // 若找到对应的Id, 通过名称找到dataEntities中对应的entity,执行更新
          const forUpdates = currentEntities
            .map((d) => {
              return { id: d.id, entity: dataEntities.find((e) => e.entityName === d.name) };
            })
            .filter((e) => e);
          trans.push(
            ...this.createOrUpdateDataEntities(
              forUpdates.map((u) => u.entity!),
              forUpdates.map((u) => u.id),
              datasetId,
            ),
          );
          newConfigs.push(
            ...forUpdates.map((f) => {
              return { jsonPath: f.entity!.jsonPath, dataEntityId: f.id };
            }),
          );
          // 若找不到dataEntities有相同名称的entity,那么该entity视为已废弃, 更新废弃标记, ingestConfig中标注废弃
          const forDeprecates = currentEntities.filter((d) => !dataEntities.find((e) => e.entityName === d.name));
          trans.push(
            this.prisma.dataEntity.updateMany({
              where: {
                id: {
                  in: forDeprecates.map((f) => f.id),
                },
              },
              data: {
                deprecated: true,
              },
            }),
          );
          // 若dataEntities存在未对应的实体,则视为新的Entity,执行新建
          const forCreates = dataEntities.filter((e) => !currentEntities.find((d) => d.name === e.entityName));
          const ids = forCreates.map(() => uuidv7());
          trans.push(...this.createOrUpdateDataEntities(forCreates, ids, datasetId));
          newConfigs.push(
            ...forCreates.map((v, i) => {
              return { jsonPath: v.jsonPath, dataEntityId: ids[i] };
            }),
          );
          trans.push(
            this.prisma.apiEndpoint.update({
              where: {
                id: endpoint.id,
              },
              data: {
                ingest_configs: newConfigs,
              },
            }),
          );
        } else {
          // 创建新的实体
          const dataIds = dataEntities.map(() => uuidv7());
          const ops = this.createOrUpdateDataEntities(dataEntities, dataIds, datasetId);
          trans.push(...ops);
          // 更新ingestConifg
          trans.push(
            this.prisma.apiEndpoint.update({
              where: {
                id: endpointId,
              },
              data: {
                ingest_configs: dataEntities.map((v, i) => {
                  return { jsonPath: v.jsonPath, dataEntityId: dataIds[i] };
                }),
              },
            }),
          );
        }
      }
      // }
      await this.prisma.$transaction(trans);
      // await this.prisma.$transaction(async (tx) => {});
      const filePath = await this.initSampleDb(datasetId);
      await this.syncApiSampleData(externalApiId, filePath);
    }
  }

  private *createOrUpdateDataEntities(dataEntities: EntityInfo[], entityIds: string[], datasetId: string) {
    const entities = dataEntities.map((e) => {
      return {
        name: e.entityName,
        description: e.entityDescription,
        dataset_id: datasetId,
        entity_type: EntityType.Table,
        structure: e.structure as Prisma.JsonObject,
        ddl_script: generateDDL(e.entityName, e.structure),
        deprecated: false,
      };
    });
    for (const [index, data] of entities.entries()) {
      const dataEntityId = entityIds[index];
      yield this.prisma.dataEntity.upsert({
        where: {
          id: dataEntityId,
        },
        update: { ...data },
        create: {
          id: dataEntityId,
          ...data,
        },
      });

      yield this.prisma.workspaceDataEntity.upsert({
        where: {
          workspace_id_data_entity_id: { workspace_id: PublicWorkspaceId, data_entity_id: dataEntityId },
        },
        update: {},
        create: {
          workspace_id: PublicWorkspaceId,
          data_entity_id: dataEntityId,
        },
      });
    }
  }

  private async initSampleDb(datasetId: string) {
    //
    const dataset = await this.prisma.dataset.findUniqueOrThrow({
      where: { id: datasetId },
      select: {
        name: true,
        namespace: true,
        workspace_id: true,
        data_entities: {
          where: {
            deprecated: false,
          },
          select: {
            id: true,
            name: true,
            structure: true,
            ddl_script: true,
          },
        },
      },
    });
    const filePath = this.configService.getSampleDataFilePath(dataset.workspace_id, dataset.namespace);
    this.logger.log(`初始化数据集"${dataset.name}"到数据库"${filePath}"`);
    const instance = await DuckDBInstance.fromCache(filePath);
    const connection = await instance.connect();
    try {
      for (const entity of dataset.data_entities) {
        if (entity.ddl_script) {
          await connection.run(entity.ddl_script);
        }
      }
      return filePath;
    } finally {
      connection.closeSync();
    }
  }

  private async syncApiSampleData(apiId: string, filePath: string) {
    const api = await this.prisma.externalApi.findUniqueOrThrow({
      where: {
        id: apiId,
      },
      select: {
        base_url: true,
        endpoints: true,
        name: true,
      },
    });
    this.logger.log(`开始同步API: "${api.name}"的样例数据`);
    const instance = await DuckDBInstance.fromCache(filePath);
    const connection = await instance.connect();
    const jobId = uuidv7();
    try {
      for (const endpoint of api.endpoints) {
        this.logger.log(`同步节点: ${endpoint.name} ...`);
        const [media, obj] = Object.entries(endpoint.request_body as JsonObject)[0];
        const baseConfig: AxiosRequestConfig = {
          baseURL: api.base_url,
          method: endpoint.method,
          url: endpoint.path,
          headers: { 'Content-Type': media },
        };

        if (endpoint.parameters) {
          // const parameters = endpoint.parameters as OpenAPIV3.ParameterObject[];
          // TODO: fill parameter
        }

        const mediaObj = obj as OpenAPIV3.MediaTypeObject;
        // console.log(mediaObj);
        for (const [key, value] of Object.entries(mediaObj.examples || {})) {
          this.logger.log(`发送示例请求: ${key} ...`);
          const config: AxiosRequestConfig = { ...baseConfig, data: value };
          const response = await this.httpService.axiosRef.request(config);
          if (response.status !== 200) {
            this.logger.warn(`请求发送失败, status:${response.status}, data: ${response.data}`);
            continue;
          }
          const queryId = uuidv7();
          for (const config of endpoint.ingest_configs as IngestConfig[]) {
            const pathObject = JSONPath.value(response.data, config.jsonPath);
            const entity = await this.prisma.dataEntity.findUniqueOrThrow({
              where: { id: config.dataEntityId },
              select: {
                structure: true,
                name: true,
              },
            });
            const dataList = Array.isArray(pathObject) ? pathObject : [pathObject];
            const structure = entity.structure as EntityStructure;
            const columns = structure.columns.map((c) => `${c.name}: '${c.dataType}'`);
            dataList.forEach((item) => {
              item[CommonColumns.JobIdColumn] = jobId;
              item[CommonColumns.QueryIdColumn] = queryId;
              item[CommonColumns.ObjectNameColumn] = key;
            });

            // console.log(dataList);
            const stream = convertToNdjsonStream(dataList);
            const savePath = this.configService.getTempFilePath(`${uuidv7()}.ndjson`);
            await saveStreamToFile(stream, savePath);
            this.logger.log(`开始写入数据到表: ${entity.name} ...`);
            await connection.run(
              `INSERT INTO "${entity.name}" SELECT * FROM read_ndjson("${savePath}", columns = {${columns.join(',')}});`,
            );
            //删除临时文件
            try {
              fs.unlinkSync(savePath);
            } catch {
              this.logger.log(`删除文件"${savePath}"失败`);
            }

            this.logger.log(`写入成功`);
          }
        }
      }
    } finally {
      connection.closeSync();
    }
  }

  private fillAxiosConfigByParameter(config: AxiosRequestConfig, parameters: OpenAPIV3.ParameterObject[]) {
    const query: any = {};
    const header = {};

    for (const parameter of parameters) {
      if (!parameter.example) {
        if (parameter.required) {
          this.logger.warn(``);
        }
        continue;
      }
      if (parameter.in === 'query') {
        query[parameter.name] = query.example;
      }
    }
  }

  private async upsertExternalApiByName(doc: OpenAPIV3.Document, baseUrl: URL) {
    const registerExternalApi = await this.prisma.externalApi.findFirst({
      where: {
        name: doc.info.title,
      },
      select: {
        id: true,
        endpoints: true,
      },
    });
    const externalApiId = registerExternalApi?.id || uuidv7();
    await this.prisma.externalApi.upsert({
      where: {
        id: externalApiId,
      },
      create: {
        id: externalApiId,
        name: doc.info.title,
        description: doc.info.description,
        base_url: baseUrl.href,
      },
      update: {
        description: doc.info.description,
        base_url: baseUrl.href,
      },
    });
    return { registerExternalApi, externalApiId };
  }

  private async upsertDatasetByName(doc: OpenAPIV3.Document) {
    const dataset = await this.prisma.dataset.findFirst({
      where: {
        name: doc.info.title,
        source_type: 'FetchFusion',
      },
      select: {
        id: true,
        namespace: true,
      },
    });
    const datasetId = dataset?.id || uuidv7();
    const result = await this.prisma.dataset.upsert({
      where: {
        id: datasetId,
      },
      create: {
        id: datasetId,
        name: doc.info.title,
        description: doc.info.description,
        source_type: 'FetchFusion',
        workspace_id: PublicWorkspaceId,
      },
      update: {
        name: doc.info.title,
        description: doc.info.description,
      },
    });
    return { datasetId: datasetId, namespace: result.namespace };
  }
}

type EntityInfo = {
  entityName: string;
  entityDescription?: string;
  jsonPath: string;
  structure: EntityStructure;
};

function convertToSchemaObject(obj: any) {
  if (obj.$ref) {
    throw new Error('Reference schema has not been supported yet');
  }
  return obj as OpenAPIV3.SchemaObject;
}

function extractEntityInfo(responseObject: OpenAPIV3.ResponseObject) {
  const content = responseObject.content?.['application/json'];
  if (content) {
    if (!content.schema) {
      throw new Error('Schema info not found');
    }
    const schema = convertToSchemaObject(content.schema);
    const entities: EntityInfo[] = [];
    const entityName = schema.title;
    const entityDescription = schema.description;
    //
    if (schema.type === 'array') {
      // 如果是array，以array的item类型作为数据实体的schema
      const itemSchema = convertToSchemaObject(schema.items);
      if (itemSchema.type !== 'object') {
        throw new Error('Item schema must be object type');
      }
      if (!entityName) {
        throw new Error('Entity name cannot be empty');
      }
      const entitySchema = mapOpenAPIToDuckDB(itemSchema);
      entities.push({
        entityName,
        entityDescription,
        jsonPath: '$',
        structure: entitySchema,
      });
    } else if (schema.type === 'object') {
      // object的情况下，需要检查是否指向了多个数据实体schema
      if (!entityName) {
        const properties = Object.entries(convertToSchemaObject(schema.properties));
        for (const [key, value] of properties) {
          const propSchema = convertToSchemaObject(value);
          if (propSchema.title) {
            if (propSchema.type === 'array') {
              const itemSchema = convertToSchemaObject(propSchema.items);
              if (itemSchema.type !== 'object') {
                throw new Error('Item schema must be object type');
              }
              const entitySchema = mapOpenAPIToDuckDB(itemSchema);
              entities.push({
                entityName: propSchema.title,
                entityDescription: propSchema.description,
                jsonPath: `$.${key}`,
                structure: entitySchema,
              });
            } else {
              const entitySchema = mapOpenAPIToDuckDB(propSchema);
              entities.push({
                entityName: propSchema.title,
                entityDescription: propSchema.description,
                jsonPath: `$.${key}`,
                structure: entitySchema,
              });
            }
          }
        }
      } else {
        const entitySchema = mapOpenAPIToDuckDB(schema);
        entities.push({
          entityName,
          entityDescription,
          jsonPath: '$',
          structure: entitySchema,
        });
      }
    } else {
      throw new Error('Only array or object type of schema is supported');
    }
    return entities;
  } else {
    throw new Error('Only media type application/json is supported');
  }
}

// 添加对于API的输出结果添加额外任务标记字段
const addFields = [
  {
    name: CommonColumns.JobIdColumn,
    dataType: 'UUID',
    nullable: true,
    description: '标记执行批次的任务ID',
  },
  {
    name: CommonColumns.QueryIdColumn,
    dataType: 'UUID',
    nullable: true,
    description: '单次查询ID',
  },
  {
    name: CommonColumns.ObjectNameColumn,
    dataType: 'VARCHAR',
    nullable: true,
    description: '查询对象名称',
  },
];

/**
 * 将 OpenAPI Schema 转换为 Entity Schema
 */
function mapOpenAPIToDuckDB(schema: OpenAPIV3.SchemaObject): EntityStructure {
  const properties = Object.entries(convertToSchemaObject(schema.properties));
  const entitySchema: EntityStructure = { columns: [] };
  for (const [propName, propValue] of properties) {
    const propSchema = convertToSchemaObject(propValue);
    const { propType, nullable } = Array.isArray(propSchema.type)
      ? (() => {
          const array = propSchema.type as unknown as string[];
          return { propType: array[0], nullable: array.includes('null') };
        })()
      : { propType: propSchema.type, nullable: propSchema.nullable || false };
    const dataType = mapToDBType(propSchema, propType);

    entitySchema.columns.push({
      name: propName,
      dataType,
      nullable,
      description: propSchema.description,
    });
  }
  entitySchema.columns.push(...addFields);
  return entitySchema;
}

function mapToDBType(propSchema: OpenAPIV3.SchemaObject, propType?: string): string {
  if (!propType) {
    propType = propSchema.type;
  }
  // 处理数组类型
  if (propType === 'array') {
    const items = (propSchema as OpenAPIV3.ArraySchemaObject).items;
    const itemsType = mapToDBType(convertToSchemaObject(items));
    return `${itemsType}[]`;
  }

  // 处理对象类型（嵌套结构）
  else if (propType === 'object') {
    const properties = propSchema.properties || {};
    if (Object.keys(properties).length === 0) {
      // 处理字典型数据
      if (propSchema.additionalProperties) {
        const valueType = mapToDBType(convertToSchemaObject(propSchema.additionalProperties));
        return `MAP(VARCHAR, ${valueType})`;
      } else {
        throw new Error('Empty object not allowed');
      }
    }

    const fields = Object.entries(properties).map(([propName, propSchema]) => {
      // 处理包含特殊字符的属性名
      const safeName = /[ -()]/.test(propName) ? `"${propName}"` : propName;
      const propType = mapToDBType(convertToSchemaObject(propSchema));
      return `${safeName} ${propType}`;
    });

    return `STRUCT(${fields.join(', ')})`;
  } else {
    return mapPrimitiveType(propType, propSchema.format);
  }
}

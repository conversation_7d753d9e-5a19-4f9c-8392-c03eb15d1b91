import type { EChartProps } from '@web/src/types'
import ReactECharts from 'echarts-for-react'
import { forwardRef, memo, useMemo } from 'react'

const EChart = forwardRef(({
    options,
    onClick,
    onContextMenu,
    setDropDownOpen,
}: EChartProps, ref) => {
    // const { themeValue } = useContext(ThemeContext)
    const onEvents = useMemo(() => ({
        click: onClick,
        contextmenu: onContextMenu,
    }), [onClick, onContextMenu])
    return (
        <ReactECharts
            option={{ ...options, animation: false }}
            // theme={themeValue}
            style={{
                height: '100%',
                width: '100%',
            }}
            ref={ref as React.LegacyRef<ReactECharts>}
            notMerge
            onChartReady={(instance) => {
                instance.getZr().on('click', (event: any) => {
                    // 没有 target 意味着鼠标/指针不在任何一个图形元素上，它是从“空白处”触发的。
                    if (!event.target) {
                        setDropDownOpen && setDropDownOpen(false)
                    }
                })
            }}
            // lazyUpdate
            onEvents={onEvents}
        />
    )
})

export default memo(EChart)

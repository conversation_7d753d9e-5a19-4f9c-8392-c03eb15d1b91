import Papa from 'papaparse';

// 定义消息类型
interface ParseRequest {
  type: 'PARSE_CSV';
  fileId: string;
  file: File;
  options?: {
    preview?: number;
    encoding?: string;
  };
}

interface ParseResponse {
  type: 'PARSE_SUCCESS' | 'PARSE_ERROR' | 'PARSE_PROGRESS';
  fileId: string;
  data?: any;
  error?: string;
  progress?: number;
}

// 字段类型定义
type FieldType = 'text' | 'integer' | 'decimal' | 'date';

interface FieldInfo {
  name: string;
  originalName: string;
  type: FieldType;
  description: string;
}

// 智能类型推断函数
function inferFieldType(sampleValues: string[]): FieldType {
  if (sampleValues.length === 0) return 'text';

  // 检查是否全部为数字
  const allNumbers = sampleValues.every(val => !isNaN(Number(val)) && val !== '');
  if (allNumbers) {
    // 检查是否包含小数点
    const hasDecimals = sampleValues.some(val => val.includes('.'));
    return hasDecimals ? 'decimal' : 'integer';
  }

  // 检查是否为日期格式（排除纯数字）
  const allDates = sampleValues.every(val => {
    const date = new Date(val);
    return !isNaN(date.getTime()) && val.length > 4; // 排除年份等短数字
  });
  
  if (allDates) {
    return 'date';
  }

  return 'text';
}

// 创建字段信息
function createFieldsInfo(data: Record<string, any>[], headers: string[]): FieldInfo[] {
  return headers.map(header => {
    // 收集前10个非空值作为样本
    const sampleValues: string[] = [];
    
    for (const row of data.slice(0, 10)) {
      const value = row[header];
      if (value && value.toString().trim()) {
        sampleValues.push(value.toString().trim());
        if (sampleValues.length >= 5) break; // 最多分析5个样本
      }
    }

    const inferredType = inferFieldType(sampleValues);

    return {
      name: header,
      originalName: header,
      type: inferredType,
      description: ''
    };
  });
}

// 监听主线程消息
self.addEventListener('message', (event: MessageEvent<ParseRequest>) => {
  const { type, fileId, file, options = {} } = event.data;
  // console.log(file)
  console.log(options)
  if (type === 'PARSE_CSV') {
    // 发送开始解析消息
    const progressResponse: ParseResponse = {
      type: 'PARSE_PROGRESS',
      fileId,
      progress: 0
    };
    self.postMessage(progressResponse);
    // 使用Papa Parse解析CSV
    Papa.parse(file, {
      header: true,
      skipEmptyLines: true,
      preview: options.preview || 100,
      encoding: options.encoding || 'UTF-8',
      dynamicTyping: false,
      transformHeader: (header: string) => header.trim(),
      // 解析步骤回调（用于显示进度）
      // step: (results) => {
      //   // 计算大概的进度（基于已解析的行数）
      //   const progress = Math.min(90, (results.meta.cursor / file.size) * 100);
      //   const progressResponse: ParseResponse = {
      //     type: 'PARSE_PROGRESS',
      //     fileId,
      //     progress: Math.round(progress)
      //   };
      //   self.postMessage(progressResponse);
      // },
      complete: (results) => {
        try {
          // 发送90%进度
          const progressResponse: ParseResponse = {
            type: 'PARSE_PROGRESS',
            fileId,
            progress: 90
          };
          self.postMessage(progressResponse);
          // console.log(results)
          // 检查是否有严重错误
          const fatalErrors = results.errors.filter(
            error => error.type === 'Delimiter' || error.type === 'Quotes'
          );
          
          if (fatalErrors.length > 0) {
            const errorResponse: ParseResponse = {
              type: 'PARSE_ERROR',
              fileId,
              error: `CSV文件格式错误: ${fatalErrors[0].message}`
            };
            self.postMessage(errorResponse);
            return;
          }

          if (!results.data || results.data.length === 0) {
            const errorResponse: ParseResponse = {
              type: 'PARSE_ERROR',
              fileId,
              error: '文件内容为空或格式不正确'
            };
            self.postMessage(errorResponse);
            return;
          }

          // 获取字段名
          const firstRow = results.data[0] as Record<string, any>;
          const headers = Object.keys(firstRow);
          if (headers.length === 0) {
            const errorResponse: ParseResponse = {
              type: 'PARSE_ERROR',
              fileId,
              error: '无法识别CSV文件的列标题'
            };
            self.postMessage(errorResponse);
            return;
          }

          // 转换数据格式
          const data = results.data.map((row, index) => ({
            key: index,
            ...(row as Record<string, any>)
          }));

          // 创建字段信息
          const fields = createFieldsInfo(results.data as Record<string, any>[], headers);

          // 发送100%进度
          const finalProgressResponse: ParseResponse = {
            type: 'PARSE_PROGRESS',
            fileId,
            progress: 100
          };
          self.postMessage(finalProgressResponse);

          // 发送成功结果
          const successResponse: ParseResponse = {
            type: 'PARSE_SUCCESS',
            fileId,
            data: {
              fileName: file.name,
              fields,
              data,
              rowCount: data.length,
              warnings: results.errors.length > 0 ? results.errors : undefined
            }
          };
          self.postMessage(successResponse);

        } catch (error) {
          const errorResponse: ParseResponse = {
            type: 'PARSE_ERROR',
            fileId,
            error: error instanceof Error ? error.message : '解析过程中发生未知错误'
          };
          self.postMessage(errorResponse);
        }
      },
      error: (error) => {
        const errorResponse: ParseResponse = {
          type: 'PARSE_ERROR',
          fileId,
          error: `文件解析失败: ${error.message}`
        };
        self.postMessage(errorResponse);
      }
    });
  }
});

// 导出类型定义供主线程使用
export type { ParseRequest, ParseResponse, FieldInfo };

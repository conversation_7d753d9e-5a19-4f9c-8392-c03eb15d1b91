import type { GraphChartProps } from '@web/src/types'
import { keepFixFloat, mapMetricToText, normalize, resolveField } from '@web/src/utils'
import { HomeOutlined } from '@ant-design/icons'
import { Breadcrumb, Button, Checkbox, Flex, Popover } from 'antd'
import { forwardRef, memo, useCallback, useEffect, useState } from 'react'
import EChart from './EChart'
import { isEmpty } from 'radash'

const metricRules = [
    ['total'],
    ['rebase'],
    ['match'],
    ['increase_rate'],
    ['tgi'],
    ['total', 'tgi'],
    ['match', 'tgi'],
    ['rebase', 'tgi'],
]
const GraphChart = forwardRef(({ data, tags, tagDims, metrics, name, chartId, graphData, drillDashBoardGraph, drillChartGraph, crossGraph }: GraphChartProps, ref: any) => {
    if (!data.length || isEmpty(data[0])) {
        return <div>暂无数据</div>
    }
    const [dropDownOpen, setDropDownOpen] = useState(false)
    const [dropDownPosition, setDropDownPosition] = useState({ x: 0, y: 0 })
    const [showBreadcrumb, setShowBreadcrumb] = useState(false)
    const [selectedTags, setSelectedTags] = useState<any[]>([])
    const [disabledTags, setDisabledTags] = useState<any[]>([])
    const [cross, setCross] = useState('')
    const [options, setOptions] = useState<any>({
        tooltip: {
            formatter: (params: any) => {
                // console.log(params);
                if (params.data.root) {
                    return ''
                }
                if (params.dataType === 'node') {
                    return metrics.filter((metric: string) => metric !== 'tgi').length > 0 ? `${resolveField(params.data.name).tag}<br/>${mapMetricToText(metrics.filter((metric: string) => metric !== 'tgi')[0])}：${keepFixFloat(params.data.value, 2, metrics.filter((metric: string) => metric !== 'tgi')[0])}` : undefined
                }
                if (params.dataType === 'edge') {
                    return metrics.includes('tgi') ? `tgi：${keepFixFloat(params.data.value, 2)}` : undefined
                }
            },
        },
        // color:themeValue.color,
        // center: [ref.current.getEchartsInstance().getWidth(), ref.current.getEchartsInstance().getHeight()],
        series: [{
            type: 'graph',
            layout: 'force',
            animation: false,
            label: {
                // position:'top',
                show: true,
                formatter: (params: any) => resolveField(params.name).tag,
            },

            // edgeLabel: {
            //     show: true
            // },
            edgeSymbol: ['none', 'arrow'],
            draggable: true,
            // circular: {
            //     rotateLabel: true
            // },
            // zoom,
            force: {
                initLayout: 'none',
                gravity: 0,
                repulsion: 300,
                edgeLength: [30, 100],
                layoutAnimation: true,
                // layoutAnimation: false,
            },
            roam: true,
            // itemStyle: {
            //         color:(params: any)=>
            //             params.data.color ? params.data.color: params.color
            // },
            lineStyle: {
                color: 'source',
                curveness: 0.4,
                opacity: 0.5,
            },
            categories: tags[0].split('_')[0]
                ? Array.from({ length: tags[0].split('_')[0] }, (_, index) => index + 1)
                    .map(level => ({ name: `${level}` }))
                : [],
            data: [
                {
                    name: graphData?.title || name,
                    id: graphData?.title || name,
                    value: 0,
                    // x: Math.random() * 1000,
                    // y: Math.random() * 1000,
                    // fixed: true,
                    root: true,
                },
                ...tags.map((tag: any, index: number) => ({
                    name: tag,
                    value: data[0][metrics.length === 2 ? metrics.filter(i => i !== 'tgi')[0] : metrics[0]][index],
                    // tgi: data[0].tgi ? normalize(data[0].tgi[index], data[0].tgi) :0,
                    itemStyle: {
                        borderColor: data[0][metrics.length === 2 ? metrics.filter(i => i !== 'tgi')[0] : metrics[0]][index] === 0 ? '#fff' : '#000',
                    },
                    // color: data[0][metrics.length === 2 ? metrics.filter(i => i !== 'tgi')[0] : metrics[0]][index] === 0 ? '#fff' : undefined,
                    metrics,
                    isExpand: false,
                    id: tag,
                    // x: Math.random() * 1000,
                    // y: Math.random() * 1000,
                    // fixed: true,
                    category: `${tag.split('_')[0]}`,
                    symbolSize: Number(tag.split('_')[0])
                        * (10 * normalize(data[0][metrics.length === 2 ? metrics.filter(i => i !== 'tgi')[0] : metrics[0]][index], data[0][metrics.length === 2 ? metrics.filter(i => i !== 'tgi')[0] : metrics[0]]) + 15),
                })),
            ],
            links: tags.map((tag: any, index: number) => ({
                source: graphData?.title || name,
                target: tag,
                value: metrics.includes('tgi') ? data[0].tgi[index] : 0,
                // id: `root-${tag}`,
                lineStyle: {
                    width: metrics.includes('tgi') && data[0].tgi.index ? normalize(data[0].tgi[index], data[0].tgi) * 10 + 1 : 1,
                    type: metrics.includes('tgi') && !data[0].tgi[index] ? 'dashed' : 'solid',
                },
            })),
        }],
    })
    useEffect(() => {
        setOptions((options: any) => ({
            ...options,
            tooltip: {
                formatter: (params: any) => {
                    if (params.data.root) {
                        return undefined
                    }
                    if (params.dataType === 'node') {
                        return metrics.filter((metric: string) => metric !== 'tgi').length > 0 ? `${resolveField(params.data.name).tag}<br/>${mapMetricToText(metrics.filter((metric: string) => metric !== 'tgi')[0])}：${keepFixFloat(params.data.value, 2, metrics.filter((metric: string) => metric !== 'tgi')[0])}` : undefined
                    }
                    if (params.dataType === 'edge') {
                        return metrics.includes('tgi') ? `tgi：${keepFixFloat(params.data.value, 2)}` : undefined
                    }
                },
            },
            series: [
                {
                    ...options?.series[0],
                    categories: tags[0].split('_')[0]
                        ? Array.from({ length: tags[0].split('_')[0] }, (_, index) => index + 1)
                            .map(level => ({ name: `${level}` }))
                        : [],
                    data: [{
                        name: graphData?.title || name,
                        id: graphData?.title || name,
                        root: true,
                        label: {
                            fontSize: 20,
                        },
                    }, ...tags.map((tag: any, index: number) => ({
                        name: tag,
                        value: data[0][metrics.length === 2 ? metrics.filter(i => i !== 'tgi')[0] : metrics[0]][index],
                        metrics,
                        isExpand: false,
                        id: tag,
                        itemStyle: {
                            borderColor: data[0][metrics.length === 2 ? metrics.filter(i => i !== 'tgi')[0] : metrics[0]][index] === 0 ? '#fff' : '#000',
                        },
                        // color: data[0][metrics.length === 2 ? metrics.filter(i => i !== 'tgi')[0] : metrics[0]][index] === 0 ? '#fff' :undefined,
                        // x: Math.random() * 1000,
                        // y: Math.random() * 1000,
                        // fixed: true,
                        category: `${tag.split('_')[0]}`,
                        symbolSize: Number(tag.split('_')[0])
                            * (10 * normalize(data[0][metrics.length === 2 ? metrics.filter(i => i !== 'tgi')[0] : metrics[0]][index], data[0][metrics.length === 2 ? metrics.filter(i => i !== 'tgi')[0] : metrics[0]]) + 15),
                    }))],
                    links: tags.map((tag: any, index: number) => ({
                        source: graphData?.title || name,
                        target: tag,
                        // id: `root-${tag}`,
                        value: metrics.includes('tgi') ? data[0].tgi[index] : 0,
                        lineStyle: {
                            width: metrics.includes('tgi') && data[0].tgi.index ? normalize(data[0].tgi[index], data[0].tgi) * 10 + 1 : 1,
                            type: metrics.includes('tgi') && !data[0].tgi[index] ? 'dashed' : 'solid',
                        },
                    })),
                },
            ],
        }))
    }, [metrics, graphData])
    const onClick = useCallback((params: any) => {
        // console.log(params);
        chartId && drillDashBoardGraph && drillDashBoardGraph(chartId, params, tagDims, metrics, setOptions)
        drillChartGraph && drillChartGraph(params, setOptions)
    }, [drillDashBoardGraph, drillChartGraph, chartId, tagDims, metrics])
    const onContextMenu = useCallback((params: any) => {
        // console.log(params)
        if (params.dataType === 'node') {
            params.event.event.preventDefault() // 阻止默认事件
            setCross(params.data.name)
            setDropDownPosition({
                x: params.event.event.pageX,
                y: params.event.event.pageY,
            })
            setDropDownOpen(true)
        }
    }, [])
    if (!metricRules.some(rule => rule.join() === metrics.join() || rule.join() === metrics.reverse().join())) {
        return <div>请选择合适的指标</div>
    }
    if (!data.length) {
        return <div>暂无数据</div>
    }
    // useWhyDidYouUpdate('GraphChart', { data, tags, tagDims, metrics, name, chartId, drillDashBoardGraph, drillChartGraph, onContextMenu, onClick, crossGraph })
    return (
        // <div style={{  height: '100%' }}>
        <Popover
            // style={{ width: 300 }}
            // onOpenChange={setDropDownOpen}
            content={(
                // {
                // items: [{
                //     key: '1',
                //     label: (
                <>
                    <Checkbox.Group
                        style={{ display: 'flex', flexDirection: 'column' }}
                        onChange={(values) => {
                            setSelectedTags(values)
                        }}
                        value={selectedTags}
                    >
                        {
                            tagDims.length > 0 && tagDims.map(item => (
                                <Checkbox key={item.tag} value={item.tag} disabled={disabledTags.includes(item.tag) || cross === item.tag}>
                                    {resolveField(item.tag).tag}
                                </Checkbox>
                            ))
                        }
                    </Checkbox.Group>
                    <Button
                        disabled={selectedTags.length === 0}
                        onClick={() => {
                            chartId && crossGraph && crossGraph(chartId, cross, selectedTags, metrics, name, setOptions)
                            setDropDownOpen(false)
                            setDisabledTags([...new Set([...disabledTags, ...selectedTags])])
                            setSelectedTags([])
                            setShowBreadcrumb(true)
                        }}
                    >
                        确定
                    </Button>
                </>
            )}
            open={dropDownOpen}
            styles={{
                root: {
                    position: 'fixed',
                    left: dropDownPosition.x,
                    top: dropDownPosition.y,
                },
            }}
        // destroyTooltipOnHide
        >
            <Flex vertical style={{ flex: 1, height: '100%' }}>
                <div style={{ flex: 1 }}>
                    <EChart
                        options={options}
                        ref={ref}
                        onContextMenu={onContextMenu}
                        onClick={onClick}
                        setDropDownOpen={setDropDownOpen}
                    // showBreadCrumb={showBreadcrumb}
                    />
                </div>
                <div
                    style={{ height: '10%' }}
                >
                    {
                        showBreadcrumb
                        && (
                            <Breadcrumb
                                // style={{ height: '10%' }}
                                separator=">"
                                items={[
                                    {
                                        title: (
                                            <HomeOutlined onClick={() => {
                                                setOptions({
                                                    ...options,
                                                    series: [
                                                        {
                                                            ...options?.series[0],
                                                            categories: tags[0].split('_')[0]
                                                                ? Array.from({ length: tags[0].split('_')[0] }, (_, index) => index + 1)
                                                                    .map(level => ({ name: `${level}` }))
                                                                : [],
                                                            data: [{
                                                                name: graphData?.title || name,
                                                                id: graphData?.title || name,
                                                                // x: Math.random() * 1000,
                                                                // y: Math.random() * 1000,
                                                                // fixed: true,
                                                                root: true,
                                                                label: {
                                                                    fontSize: 20,
                                                                },
                                                            }, ...tags.map((tag: any, index: number) => ({
                                                                name: tag,
                                                                metrics,
                                                                value: data[0][metrics.length === 2 ? metrics.filter(i => i !== 'tgi')[0] : metrics[0]][index],
                                                                isExpand: false,
                                                                id: tag,
                                                                itemStyle: {
                                                                    borderColor: data[0][metrics.length === 2 ? metrics.filter(i => i !== 'tgi')[0] : metrics[0]][index] === 0 ? '#fff' : '#000',
                                                                },
                                                                // x: Math.random() * 1000,
                                                                // y: Math.random() * 1000,
                                                                // fixed: true,
                                                                category: `${tag.split('_')[0]}`,
                                                                symbolSize: Number(tag.split('_')[0])
                                                                    * (10 * normalize(data[0][metrics.length === 2 ? metrics.filter(i => i !== 'tgi')[0] : metrics[0]][index], data[0][metrics.length === 2 ? metrics.filter(i => i !== 'tgi')[0] : metrics[0]]) + 15),
                                                            }))],
                                                            links: tags.map((tag: any, index: number) => ({
                                                                source: graphData?.title || name,
                                                                target: tag,
                                                                // id: `root-${tag}`,
                                                                value: metrics.includes('tgi') ? data[0].tgi[index] : 0,
                                                                lineStyle: {
                                                                    width: metrics.includes('tgi') ? normalize(data[0].tgi[index], data[0].tgi) * 10 + 1 : 1,
                                                                    type: metrics.includes('tgi') && data[0].tgi[index] === 0 ? 'dashed' : 'solid',
                                                                },
                                                            })),
                                                        },
                                                    ],
                                                })
                                                setCross('')
                                                setSelectedTags([])
                                                setDisabledTags([])
                                                setShowBreadcrumb(false)
                                            }}
                                            />
                                        ),
                                    },
                                    {
                                        title: `${graphData?.title || name}X${resolveField(cross).tag}`,
                                    },
                                ]}
                            />
                        )
                    }
                </div>
            </Flex>
        </Popover>
        // </div>
    )
})
export default memo(GraphChart)

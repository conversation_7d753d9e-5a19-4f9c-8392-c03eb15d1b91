import type { SankeyChartProps } from '@web/src/types'
import { forwardRef } from 'react'
import EChart from './EChart'

function SankeyChart({ data }: SankeyChartProps, ref: any) {
    const options = {
        series: {
            type: 'sankey',
            // layout,
            emphasis: {
                focus: 'adjacency',
            },
            data,
        },
    }
    return (
        <EChart
            options={options}
            ref={ref}
        />
    )
}
export default forwardRef(SankeyChart)

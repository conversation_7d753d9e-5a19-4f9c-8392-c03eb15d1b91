import type { WordCloudChartProps } from '@web/src/types'
import { Flex, Select } from 'antd'
import { forwardRef, useState } from 'react'
import EChart from './EChart'
import 'echarts-wordcloud'
import { isEmpty } from 'radash'
const keywordSourceOptions: any[] = []

function WordCloud({ data, views, metrics }: WordCloudChartProps, ref: any) {
    const [currentView, setCurrentView] = useState(views[0].name)
    if (!data.length || isEmpty(data[0])) {
        return <div>暂无数据</div>
    }
    const options = {
        series: [{
            type: 'wordCloud',
            name: currentView,
            width: '400%',
            height: '200%',
            gridSize: 5,
            sizeRange: [12, 60],
            rotationRange: [0, 0],
            rotationStep: 0,
            layoutAnimation: false,
            drawOutOfBound: false,
            shape: 'square',
            data: data[views.indexOf(views.find(view => view.name === currentView))][metrics[0]],
        }],
    }
    return (
        <>
            <Select
                value={currentView}
                placeholder="请选择视图"
                options={views.map(view => ({ label: keywordSourceOptions.find(keyword => keyword.value === view.name)?.label ?? view.name, value: view.name }))}
                style={{ zIndex: 100, width: 150 }}
                onChange={setCurrentView}
            />
            <Flex style={{ flex: 1 }} vertical align="center" justify="center">
                <EChart
                    options={options}
                    ref={ref}
                />
            </Flex>
        </>
    )
}
export default forwardRef(WordCloud)

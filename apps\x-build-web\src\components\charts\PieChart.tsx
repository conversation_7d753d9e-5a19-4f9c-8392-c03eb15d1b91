import type { PieChartProps } from '@web/src/types'
import { forwardRef } from 'react'
import { keepFixFloat, mapMetricToText, resolveField } from '@web/src/utils'
import EChart from './EChart'
import { isEmpty } from 'radash'

function PieChart({ data, tags, metrics, tagDims, path, chartId, setPath, drillDown }: PieChartProps, ref: any) {
    if (metrics.length !== 1) {
        return <div>请选择一个指标</div>
    }
    if (!data.length || isEmpty(data[0])) {
        return <div>暂无数据</div>
    }
    const onclick = (params: any) => {
        chartId && drillDown && drillDown(chartId, params.name, tagDims)
        path && setPath && setPath([...path, params.name])
    }
    const options = {
        tooltip: {
            trigger: 'item',
            formatter: (params: any) => `${resolveField(params.name).tag}<br/>${params.marker} ${mapMetricToText(metrics[0])}：${keepFixFloat(params.value, 2, metrics[0])}`,
        },
        // title: {
        //     text: name || '',
        // },
        legend: {
            textStyle: {
                fontSize: '14px',
            },
            // top: 'center',
            // left: 'right',
            // orient: 'vertical',
            type: 'scroll',
            data: tags,
            formatter: (name: string) => resolveField(name).tag,
        },
        series: [
            {
                type: 'pie',
                data: data[0][metrics[0]]?.map((item: any, index: number) => ({ name: tags[index], value: item })),
                radius: ['20%', '50%'],
                avoidLabelOverlap: false,
                label: {
                    show: false,
                    position: 'center',
                    formatter: (params: any) => resolveField(params.name).tag,
                },
                // emphasis: {
                //     label: {
                //         show: true,
                //         // position:'center',
                //         fontSize: 40,
                //         fontWeight: 'bold',
                //     }
                // },
                labelLine: {
                    show: false,
                },
            },
        ],
    }
    return (
        <EChart
            options={options}
            ref={ref}
            onClick={onclick}
        />
    )
}
export default forwardRef(PieChart)

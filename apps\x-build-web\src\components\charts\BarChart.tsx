import type { BarChartProps } from '@web/src/types'
import { forwardRef, useCallback } from 'react'
import { keepFixFloat, mapMetricToText, resolveField } from '@web/src/utils'
import EChart from './EChart'
import { isEmpty } from 'radash'

function BarChart({ data, tags, tagDims, period, metrics, views, chartId, setPath, drillDown }: BarChartProps, ref: any) {
    const onclick = useCallback((params: any) => {
        chartId && drillDown && drillDown(chartId, params.name, tagDims)
        setPath && setPath(pre => [...pre, params.name])
    }, [chartId, tagDims, setPath, drillDown])
    if (metrics.length !== 1) {
        return <div>请选择一个指标</div>
    }
    if (!data.length || isEmpty(data[0])) {
        return <div>暂无数据</div>
    }
    const options = {
        tooltip: {
            trigger: 'axis',
            // show:views.length<=1,
            formatter: (params: any) => {
                if (views.length > 1) {
                    // console.log(params);
                    return `${resolveField(params[0].name).tag}<br/>${params.map((i: any) => `${i.marker}${resolveField(i.seriesName).tag} ${mapMetricToText(metrics[0])}：${keepFixFloat(i.value, 2, metrics[0])}`).join('<br/>')}`
                }
                if (period && period.length > 0) {
                    return `${params[0].name}<br/>${params.map((i: any) => `${i.marker}${resolveField(i.seriesName).tag} ${mapMetricToText(metrics[0])}：${keepFixFloat(i.value, 2, metrics[0])}`).join('<br/>')}`
                }
                return `${resolveField(params[0].name).tag}<br/> ${mapMetricToText(metrics[0])}：${keepFixFloat(params[0].value, 2, metrics[0])}`
            },
        },
        legend: {
            show: true,
            textStyle: {
                fontSize: '14px',
            },
            type: 'scroll',
            formatter: (name: string) => resolveField(name).tag,
        },
        xAxis: {
            type: 'category',
            axisLabel: {
                interval: 0,
                rotate: -40,
                fontSize: '14px',
                formatter: views.length > 1 || (period && period.length > 0) ? undefined : (value: string) => resolveField(value).tag,
            },
            data: views.length > 1
                ? views.map(view => view.name)
                : period && period.length > 0
                    ? period.map((date: any) => date.toString())
                    : tags,
        },
        yAxis: {
            type: 'value',
            axisLabel: {
                fontSize: '14px',
                formatter: (value: any) => keepFixFloat(value, 2, metrics[0]),
            },
        },
        series:
            views.length > 1
                ? tags.map((tag, index) => ({
                    type: 'bar',
                    name: tag,
                    emphasis: {
                        focus: 'series',
                    },
                    markLine: {
                        data: [{
                            type: 'average',
                            name: '平均值',
                            label: {
                                fontSize: '13px',
                                formatter: (params: any) => keepFixFloat(params.value, 2, metrics[0]),
                            },
                        }],
                    },
                    realtimeSort: true,
                    data: views.map((_, index2) => data[index2][metrics[0]][index]),
                }))
                : period && period.length > 0
                    ? tags.map((tag, index) => ({
                        type: 'bar',
                        name: tag,
                        emphasis: {
                            focus: 'series',
                        },
                        markLine: {
                            data: [{
                                type: 'average',
                                name: '平均值',
                                label: {
                                    fontSize: '13px',
                                    formatter: (params: any) => keepFixFloat(params.value, 2, metrics[0]),
                                },
                            }],
                        },
                        data: data[0][metrics[0]][index],
                    }))
                    : [{
                        type: 'bar',
                        realtimeSort: true,
                        data: data[0][metrics[0]] ?? [],
                        markLine: {
                            data: [{
                                type: 'average',
                                name: '平均值',
                                label: {
                                    fontSize: '13px',
                                    formatter: (params: any) => keepFixFloat(params.value, 2, metrics[0]),
                                },
                            }],
                        },
                    }],
    }
    return (
        <EChart
            options={options}
            onClick={onclick}
            ref={ref}
        />
    )
}
export default forwardRef(BarChart)

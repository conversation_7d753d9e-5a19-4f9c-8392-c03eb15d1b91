import type { DoughnutChartProps } from '@web/src/types'
import { forwardRef } from 'react'
import { keepFixFloat, mapMetricToText, resolveField } from '@web/src/utils'
import EChart from './EChart'
import { isEmpty } from 'radash'

function Doughnut<PERSON>hart({ data, roseType, tags, metrics, tagDims, path, chartId, setPath, drillDown }: DoughnutChartProps, ref: any) {
    if (metrics.length !== 1) {
        return <div>请选择一个指标</div>
    }
    if (!data.length || isEmpty(data[0])) {
        return <div>暂无数据</div>
    }
    const onclick = (params: any) => {
        chartId && drillDown && drillDown(chartId, params.name, tagDims)
        path && setPath && setPath([...path, params.name])
    }
    const options = {
        tooltip: {
            trigger: 'item',
            formatter: (params: any) => `${resolveField(params.name).tag}<br/>${params.marker} ${mapMetricToText(metrics[0])}：${keepFixFloat(params.value, 2, metrics[0])}`,
        },
        // title: {
        //     text: name || '',
        //     // lineHeight:20
        // },
        legend: {
            textStyle: {
                fontSize: '14px',
            },
            type: 'scroll',
            // top: 'center',
            // left: 'right',
            // orient: 'vertical',
            data: tags,
            formatter: (name: string) => resolveField(name).tag,
        },
        series: [
            {
                roseType,
                type: 'pie',
                radius: ['20%', '50%'],
                itemStyle: {
                    borderRadius: 5,
                },
                label: {
                    show: false,
                    formatter: (params: any) => resolveField(params.name).tag,
                },
                data: data[0][metrics[0]]?.map((item: any, index: number) => ({ name: tags[index], value: item })),
            },
        ],
    }
    return (
        <EChart
            options={options}
            ref={ref}
            onClick={onclick}
        />
    )
}
export default forwardRef(DoughnutChart)

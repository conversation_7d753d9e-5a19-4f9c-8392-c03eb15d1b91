import { createRoot } from 'react-dom/client'
import './index.css'
import App from './App.tsx'
import React from 'react';
import { createBrowserRouter, Navigate, RouteObject, RouterProvider } from 'react-router-dom';
import Workspace from './pages/Workspace.tsx';
import PublicWorkspace from './pages/PublicWorkspace.tsx';
import Operation from './pages/Operation.tsx';
import PublishManage from './pages/PublishManage.tsx';
import WorkspaceDetail from './components/WorkspaceDetail.tsx';
import ViewDetail from './components/ViewDetail.tsx';
import WorkSpaceDataSet from './components/WorkSpaceDataSet.tsx';
import FileUpload from './pages/FileUpload.tsx';

export const routes: RouteObject[] = [
      {
            path: '/',
            Component: () => <App />,
            children: [
                  {
                        index: true,
                        Component: () => <Navigate to="/workspace" replace />,
                  },
                  {
                        path: 'workspace',
                        children: [
                              {
                                    index: true,
                                    Component: () => <Workspace />
                              },
                              {
                                    path: ':id',
                                    children: [
                                          {
                                                // index: true,
                                                Component: () => <WorkspaceDetail />,
                                                children: [
                                                      {
                                                            index: true,
                                                            path: 'dataset',
                                                            Component: () => <WorkSpaceDataSet />
                                                      },
                                                      {
                                                            path: 'addView',
                                                            Component: () => <ViewDetail />
                                                      },
                                                      {
                                                            path: 'operation',
                                                            Component: () => <Operation />
                                                      },
                                                      {
                                                            path: 'publishManage',
                                                            Component: () => <PublishManage />
                                                      }
                                                ]
                                          }

                                    ]
                              },

                        ]
                  },
                  {
                        path: 'publicWorkspace',
                        children: [
                              {
                                    index: true,
                                    Component: () => <PublicWorkspace />
                              },
                              {
                                    path: 'addView',
                                    Component: () => <ViewDetail />
                              },
                              {
                                    path: 'operation',
                                    Component: () => <Operation />
                              },
                              {
                                    path: 'publishManage',
                                    Component: () => <PublishManage />
                              },
                              {
                                    path: 'upload',
                                    Component: () => <FileUpload />
                              }
                        ]
                  },
            ],
      },
]
const router = createBrowserRouter(routes);
createRoot(document.getElementById('root')!).render(
      // <React.StrictMode>
            <RouterProvider router={router} />
      // </React.StrictMode>
)

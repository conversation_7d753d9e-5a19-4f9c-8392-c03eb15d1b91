import { Readable } from 'stream';
import * as ndjson from 'ndjson';
import * as fs from 'fs';

export function joinUrl(base: string, path: string): string {
  const normalizedBase = base.replace(/\/+$/, '');
  const normalizedPath = path.replace(/^\/+/, '');
  return `${normalizedBase}/${normalizedPath}`;
}

export function convertToNdjsonStream<T extends object>(objects: T[]): Readable {
  const objectStream = Readable.from(objects, { objectMode: true });
  return objectStream.pipe(ndjson.stringify());
}

export function saveStreamToFile(stream: NodeJS.ReadableStream, path: string) {
  return new Promise<void>((resolve, reject) => {
    const writeStream = fs.createWriteStream(path);
    stream.pipe(writeStream);
    writeStream.on('finish', resolve);
    writeStream.on('error', reject);
  });
}

export type PropertyTypes<T> = {
  [K in keyof T]: T[K];
};

// import { DatabaseService } from "./database.service.ts";
import { Router, Query } from 'nestjs-trpc';
import { Inject } from '@nestjs/common';
import { z } from 'zod';

const dogSchema = z.object({
  name: z.string(),
  breed: z.enum(['Labrador', 'Corgi', '<PERSON>agle', 'Golden Retriver']).or(z.string()),
});

@Router()
export class AppRouter {
  // constructor(@Inject(DatabaseService) private databaseService: DatabaseService) { }

  @Query({ output: z.array(dogSchema) })
  findAll(): z.infer<typeof dogSchema>[] {
    const dogs = [
      {
        name: '<PERSON><PERSON>',
        breed: 'Corgi',
      },
    ];
    return dogs;
  }
}

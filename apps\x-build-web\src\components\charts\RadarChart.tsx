import type { RadarChartProps } from '@web/src/types'
import { forwardRef } from 'react'
import { keepFixFloat, mapMetricToText, resolveField } from '@web/src/utils'
import EChart from './EChart'
import { isEmpty } from 'radash'

function RadarChart({ data, tags, metrics, views, tagDims, path, chartId, setPath, drillDown }: RadarChartProps, ref: any) {
    if (metrics.length !== 1) {
        return <div>请选择一个指标</div>
    }
    if (!data.length || isEmpty(data[0])) {
        return <div>暂无数据</div>
    }
    const options = {
        tooltip: {
            trigger: 'item',
            formatter: (params: any) => {
                // console.log(params);
                return `${params.marker} ${mapMetricToText(metrics[0])}：${keepFixFloat(params.value, 2, metrics[0])}`
            },
        },
        // title: {
        //     text: name || '',
        //     // lineHeight:20
        // },
        legend: {
            show: views.length > 1,
            data: views.map(view => view.name),
            type: 'scroll',
            textStyle: {
                fontSize: '14px',
            },
            // orient: 'vertical',
            // top: 'center',
            // left: 'right',
            // align: 'left',
        },
        radar: {
            // shape: 'circle',
            indicator: tags.map(tag => ({ name: tag, max: Math.max(...data.map(item => Math.max(...item[metrics[0]]))) * 1.2 })),
            // radius: 70
            // nameGap:8,
            // splitNumber:2
            axisName: {
                formatter: (value: any) => resolveField(value).tag,
            },
        },
        series:
            [
                {
                    type: 'radar',
                    data: views.map((view, index) => ({
                        name: view.name,
                        tag: tags[index],
                        value: data[index][metrics[0]],
                    })),
                    label: {
                        position: 'inside',
                    },
                },
            ],
    }
    const onclick = (params: any) => {
        chartId && drillDown && drillDown(chartId, options.radar.indicator[params.event.topTarget.__dimIdx].name, tagDims)
        path && setPath && setPath([...path, options.radar.indicator[params.event.topTarget.__dimIdx].name])
    }
    return (
        <EChart
            options={options}
            ref={ref}
            onClick={onclick}
        />
    )
}
export default forwardRef(RadarChart)

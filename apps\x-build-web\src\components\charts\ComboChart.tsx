import type { ComboChartProps } from '@web/src/types'
import { forwardRef, useState } from 'react'
import { keepFixFloat, mapMetricToText, resolveField } from '@web/src/utils'
import EChart from './EChart'
import { isEmpty } from 'radash'

const metricRules = [
    ['total', 'rebase'],
    ['total', 'increase_rate'],
    ['total', 'tgi'],
    ['match', 'tgi'],
    ['match', 'increase_rate'],
    ['match', 'rebase'],
    ['rebase', 'increase_rate'],
    ['rebase', 'tgi'],
    ['increase_rate', 'tgi'],
]
function ComboChart({ data, tags, metrics, views, tagDims, path, paths, chartId, setPath, drillDown }: ComboChartProps, ref: any) {
    const [seriesType, setSeriesType] = useState('')
    if (metrics.length !== 2) {
        return <div>请选择两个指标</div>
    }
    if (metricRules.every(rule => metrics.includes(rule))) {
        return <div>请选择合适的两个指标</div>
    }
    if (!data.length || isEmpty(data[0])) {
        return <div>暂无数据</div>
    }
    const onclick = (params: any) => {
        // console.log(params);
        chartId && drillDown && drillDown(chartId, params.name, tagDims)
        path && setPath && setPath([...path, params.name])
        // setSeriesName(params.seriesName)
        if ((path && path.length === 0) || (paths && paths.find((item: any) => item.id === chartId).path.length === 0))
            setSeriesType(params.seriesType)
    }
    // useWhyDidYouUpdate('ComboChart', { data, tags, metrics, views, name, path, paths, chartId, setPath, drillDown })
    const options = (paths && paths.find((item: any) => item.id === chartId).path.length > 0) || (path && path.length > 0)
        ? {
            tooltip: {
                trigger: 'axis',
                formatter: (params: any) => {
                    // console.log(params);
                    return `${resolveField(params[0].name).tag} <br/>${mapMetricToText(metrics[seriesType === 'bar' ? 0 : 1])}:${params[0].value}`
                },
            },
            // title: {
            //     text: name || '',
            // },
            // legend: {
            //     top: 'center',
            //     left: 'right',
            //     orient: 'vertical',
            //     type: 'scroll',
            //     data: tags,
            //     formatter: (name: string) =>  resolveField(name).tag,
            // },
            xAxis: {
                type: 'category',
                axisLabel: {
                    interval: 0,
                    rotate: -40,
                    fontSize: '14px',
                    formatter: (value: string) => resolveField(value).tag,
                },
                data: tags,
            },
            yAxis: {
                type: 'value',
                axisLabel: {
                    fontSize: '14px',
                    formatter: (value: string) => keepFixFloat(value, 2, metrics[0]),
                },
            },
            series:
                // views.length > 1
                //     ? tags.map((tag, index) => ({
                //         type: 'bar',
                //         name: tag,
                //         emphasis: {
                //             focus: 'series',
                //         },
                //         realtimeSort: true,
                //         data: views.map((view, index2) => data[index2][metrics[seriesType=='bar'?0:1]][seriesName]),
                //     }))
                // :
                [{
                    type: 'bar',
                    realtimeSort: true,
                    data: data[0][metrics[seriesType === 'bar' ? 0 : 1]],
                }],
        }
        : {
            tooltip: {
                trigger: 'axis',
                formatter: (params: any) => {
                    // console.log(params);
                    return views.length > 1
                        ? undefined
                        : ` ${resolveField(params[0].name).tag}<br/> ${params[0].marker}${mapMetricToText(metrics[0])}：${keepFixFloat(params[0].value[metrics[0]], 2, metrics[0])}
            <br/> ${params[1].marker}${mapMetricToText(metrics[1])}：${keepFixFloat(params[1].value[metrics[1]], 2, metrics[1])}`
                },
            },
            // title: {
            //     text: name || '',
            // },
            dataset: [{
                source:
                    // views.length > 1 ?
                    //     views.map((views,index)=> ({
                    //         ...tags.map((tag, index2) => ({
                    //             tag,
                    //             [metrics[0]]: data[index][metrics[0]][index2],
                    //             [metrics[1]]: data[index][metrics[1]][index2]
                    //         }))
                    // }))
                    //     :
                    data[0][metrics[0]] ?
                        tags.map((tag, index) => ({
                            tag,
                            [metrics[0]]: data[0][metrics[0]][index],
                            [metrics[1]]: data[0][metrics[1]][index],
                        })) : [],
            }, {
                transform: {
                    type: 'sort',
                    config: [
                        {
                            dimension: metrics[0],
                            order: 'desc',
                        },
                        {
                            dimension: metrics[1],
                            order: 'desc',
                        },
                    ],
                    print: true,
                },
            }],
            legend: {
                textStyle: {
                    fontSize: '14px',
                },
                // data: views.length > 1 ? tags : metrics,
                data: views.length > 1 ? views.map(view => view.name) : metrics,
                // top: 'center',
                // left: 'right',
                // orient: 'vertical',
                type: 'scroll',
                formatter: (name: string) => views.length > 1 ? name : resolveField(name).tag,
            },
            xAxis: {
                type: 'category',
                axisLabel: {
                    interval: 0,
                    rotate: -40,
                    fontSize: '14px',
                    formatter: (value: string) => resolveField(value).tag,
                },
                data: views.length > 1
                    // ? views.map(view => view.name)
                    ? tags
                    : undefined,
            },
            yAxis: metrics.map(metric => ({
                type: 'value',
                name: metric,
                axisLabel: {
                    formatter: (value: string) => keepFixFloat(value, 2, metric),
                },
            })),
            series:
                views.length > 1
                    ? views.map((view, index) => {
                        return metrics.map((metric, index2) => {
                            return index2 === 0
                                ? {
                                    type: 'bar',
                                    yAxisIndex: index2,
                                    name: view.name,
                                    // realtimeSort: true,
                                    emphasis: {
                                        focus: 'series',
                                    },
                                    markLine: {
                                        data: [{
                                            type: 'average',
                                            name: '平均值',
                                            label: {
                                                fontSize: '13px',
                                                formatter: (params: any) => keepFixFloat(params.value, 2, metrics[0]),
                                            },
                                        }],
                                    },
                                    // datasetIndex: 1,
                                    data: tags.map((_, index3) => data[index][metric][index3]),
                                }
                                : {
                                    type: 'line',
                                    yAxisIndex: index2,
                                    name: view.name,
                                    emphasis: {
                                        focus: 'series',
                                    },
                                    smooth: true,
                                    markLine: {
                                        data: [{
                                            type: 'average',
                                            name: '平均值',
                                            label: {
                                                fontSize: '13px',
                                                formatter: (params: any) => keepFixFloat(params.value, 2, metrics[0]),
                                            },
                                        }],
                                    },
                                    // markPoint: {
                                    //     data: [
                                    //         { type: 'max', name: 'Max' },
                                    //         { type: 'min', name: 'Min' }
                                    //     ]
                                    // },
                                    // datasetIndex: 1,
                                    data: tags.map((_, index3) => data[index][metric][index3]),
                                }
                        })
                    }).flat()
                    : metrics.map((metric, index) => (index === 0
                        ? {
                            type: 'bar',
                            name: metric,
                            yAxisIndex: index,
                            // realtimeSort: true,
                            emphasis: {
                                focus: 'series',
                            },
                            markLine: {
                                data: [{
                                    type: 'average',
                                    name: '平均值',
                                    label: {
                                        fontSize: '13px',
                                        formatter: (params: any) => keepFixFloat(params.value, 2, metrics[0]),
                                    },
                                }],
                            },
                            datasetIndex: 1,
                            // data: data[0][metric]
                        }
                        : {
                            type: 'line',
                            name: metric,
                            yAxisIndex: index,
                            emphasis: {
                                focus: 'series',
                            },
                            smooth: true,
                            // markPoint: {
                            //     data: [
                            //         { type: 'max', name: 'Max' },
                            //         { type: 'min', name: 'Min' }
                            //     ]
                            // },
                            markLine: {
                                data: [{
                                    type: 'average',
                                    name: '平均值',
                                    label: {
                                        fontSize: '13px',
                                        formatter: (params: any) => keepFixFloat(params.value, 2, metrics[0]),
                                    },
                                }],
                            },
                            datasetIndex: 1,
                            // data: data[0][metric]
                        })),
        }

    return (
        <EChart
            options={options}
            ref={ref}
            onClick={onclick}
        />
    )
}
export default forwardRef(ComboChart)

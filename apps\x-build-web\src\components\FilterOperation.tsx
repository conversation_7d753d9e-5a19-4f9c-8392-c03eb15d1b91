import { PlusOutlined, DeleteOutlined } from "@ant-design/icons";
import { Button, Select, Input, Radio } from "antd";
import { nanoid } from 'nanoid';

// 过滤条件节点类型
type FilterNode = {
    id: string;
    type: 'condition' | 'group';
    // 条件节点属性
    field?: string;
    operator?: string;
    value?: string;
    valueEnd?: string;
    uniqueValues?: boolean;
    // 组节点属性
    logic?: 'AND' | 'OR';
    children?: FilterNode[];
};

// Helper function to get operator options based on field type
const getOperatorOptions = (fieldName: string) => {
    // Check if field is numeric
    const isNumeric = ['voice', 'interact', 'like', 'comment', 'repost', 'read', 'collect'].includes(fieldName);
    if (isNumeric) {
        return [
            { label: '大于 (>)', value: '>' },
            { label: '小于 (<)', value: '<' },
            { label: '等于 (=)', value: '=' },
            { label: '大于等于 (>=)', value: '>=' },
            { label: '小于等于 (<=)', value: '<=' },
            { label: '在区间内', value: 'between' },
            { label: '不在区间内', value: 'not_between' },
            { label: '为空', value: 'is_null' },
            { label: '不为空', value: 'is_not_null' },
        ];
    } else {
        return [
            { label: '等于 (=)', value: '=' },
            { label: '不等于 (!=)', value: '!=' },
            { label: '为空', value: 'is_null' },
            { label: '不为空', value: 'is_not_null' },
            { label: '以..开头', value: 'starts_with' },
            { label: '以..结尾', value: 'ends_with' },
            { label: '不以..开头', value: 'not_starts_with' },
            { label: '不以..结尾', value: 'not_ends_with' },
            { label: '模糊匹配', value: 'contains' },
            { label: '模糊不匹配', value: 'not_contains' },
        ];
    }
};

// 组件Props类型定义
interface FilterOperationProps {
    filterNodes: FilterNode[];
    setFilterNodes: React.Dispatch<React.SetStateAction<FilterNode[]>>;
    fieldKeys: string[];  // 当前表字段
};
const FilterOperation = ({ filterNodes, setFilterNodes, fieldKeys }: FilterOperationProps) => {
    // 查找节点
    const findNode = (nodes: FilterNode[], id: string): FilterNode | null => {
        for (const node of nodes) {
            if (node.id === id) return node;
            if (node.children) {
                const found = findNode(node.children, id);
                if (found) return found;
            }
        }
        return null;
    };

    // 更新节点
    const updateNode = (nodes: FilterNode[], id: string, updates: Partial<FilterNode>): FilterNode[] => {
        return nodes.map(node => {
            if (node.id === id) {
                return { ...node, ...updates };
            }
            if (node.children) {
                return { ...node, children: updateNode(node.children, id, updates) };
            }
            return node;
        });
    };

    // 删除节点
    const deleteNode = (nodes: FilterNode[], id: string): FilterNode[] => {
        return nodes.filter(node => {
            if (node.id === id) return false;
            if (node.children) {
                node.children = deleteNode(node.children, id);
            }
            return true;
        });
    };

    // 添加子节点
    const addChildNode = (nodes: FilterNode[], parentId: string, newNode: FilterNode): FilterNode[] => {
        return nodes.map(node => {
            if (node.id === parentId) {
                return {
                    ...node,
                    children: [...(node.children || []), newNode]
                };
            }
            if (node.children) {
                return { ...node, children: addChildNode(node.children, parentId, newNode) };
            }
            return node;
        });
    };

    // 渲染条件节点
    const renderConditionNode = (node: FilterNode, level: number = 0) => (
        <div key={node.id} className="flex items-center mb-2 gap-2" style={{ marginLeft: level * 20 }}>
            {/* Field selector */}
            <Select
                placeholder="请选择字段"
                style={{ width: 180 }}
                value={node.field || undefined}
                onChange={(value) => {
                    setFilterNodes(prev => updateNode(prev, node.id, { field: value }));
                }}
                options={fieldKeys.map(key => ({ label: key, value: key }))}
            />

            {/* Operator selector */}
            {node.field && (
                <Select
                    placeholder="请选择条件"
                    style={{ width: 180 }}
                    value={node.operator || undefined}
                    onChange={(value) => {
                        setFilterNodes(prev => updateNode(prev, node.id, { operator: value }));
                    }}
                    options={getOperatorOptions(node.field)}
                />
            )}

            {/* Value input */}
            {node.field && node.operator &&
                !['is_null', 'is_not_null'].includes(node.operator) && (
                    <>
                        <Input
                            placeholder="请输入值"
                            style={{ width: 180 }}
                            value={node.value || ''}
                            onChange={(e) => {
                                setFilterNodes(prev => updateNode(prev, node.id, { value: e.target.value }));
                            }}
                        />

                        {/* Second value for between operators */}
                        {['between', 'not_between'].includes(node.operator) && (
                            <>
                                <span>至</span>
                                <Input
                                    placeholder="请输入结束值"
                                    style={{ width: 180 }}
                                    value={node.valueEnd || ''}
                                    onChange={(e) => {
                                        setFilterNodes(prev => updateNode(prev, node.id, { valueEnd: e.target.value }));
                                    }}
                                />
                            </>
                        )}
                    </>
                )}

            {/* Unique values selector */}
            {node.field && (
                <Select
                    style={{ width: 180 }}
                    value={node.uniqueValues ? '唯一项' : '全部'}
                    onChange={(value) => {
                        setFilterNodes(prev => updateNode(prev, node.id, { uniqueValues: value === '唯一项' }));
                    }}
                    options={[
                        { label: '全部', value: '全部' },
                        { label: '唯一项', value: '唯一项' },
                    ]}
                />
            )}

            {/* Delete condition button */}
            <Button
                type="text"
                icon={<DeleteOutlined />}
                onClick={() => {
                    setFilterNodes(prev => deleteNode(prev, node.id));
                }}
            />
        </div>
    );

    // 渲染组节点
    const renderGroupNode = (node: FilterNode, level: number = 0) => (
        <div key={node.id} className="mb-3 p-2 border border-gray-200 rounded" style={{ marginLeft: level * 20 }}>
            {/* 逻辑关系选择器 - 只有当子节点数量>=2时才显示 */}
            <div className="flex items-center mb-2 gap-2">
                {(node.children?.length || 0) >= 2 && (
                    <Radio.Group
                        value={node.logic || 'AND'}
                        onChange={(e) => {
                            setFilterNodes(prev => updateNode(prev, node.id, { logic: e.target.value }));
                        }}
                        options={[
                            { label: '且', value: 'AND' },
                            { label: '或', value: 'OR' },
                        ]}
                        optionType="button"
                        buttonStyle="solid"
                        size="small"
                    />
                )}
                <Button
                    type="dashed"
                    size="small"
                    icon={<PlusOutlined />}
                    onClick={() => {
                        const newCondition: FilterNode = {
                            id: nanoid(),
                            type: 'condition',
                            field: '',
                            operator: '',
                            value: '',
                            uniqueValues: false
                        };
                        setFilterNodes(prev => addChildNode(prev, node.id, newCondition));
                    }}
                >
                    添加条件
                </Button>
                <Button
                    type="dashed"
                    size="small"
                    icon={<PlusOutlined />}
                    onClick={() => {
                        const newGroup: FilterNode = {
                            id: nanoid(),
                            type: 'group',
                            logic: 'AND',
                            children: []
                        };
                        setFilterNodes(prev => addChildNode(prev, node.id, newGroup));
                    }}
                >
                    添加组
                </Button>
                <Button
                    type="text"
                    icon={<DeleteOutlined />}
                    onClick={() => {
                        setFilterNodes(prev => deleteNode(prev, node.id));
                    }}
                />
            </div>
            {/* 渲染子节点 */}
            {node.children?.map(child => renderNode(child, level + 1))}
        </div>
    );

    // 渲染节点（条件或组）
    const renderNode = (node: FilterNode, level: number = 0) => {
        if (node.type === 'condition') {
            return renderConditionNode(node, level);
        } else {
            return renderGroupNode(node, level);
        }
    };

    return (
        <div className="mb-4 p-2 rounded-md">
            <div className="flex items-center mb-2">
                <span className="font-bold mr-2">筛选条件</span>
                <Button
                    type="primary"
                    size="small"
                    icon={<PlusOutlined />}
                    onClick={() => {
                        const newCondition: FilterNode = {
                            id: nanoid(),
                            type: 'condition',
                            field: '',
                            operator: '',
                            value: '',
                            uniqueValues: false
                        };
                        setFilterNodes(prev => [...prev, newCondition]);
                    }}
                >
                    添加条件
                </Button>
                <Button
                    type="primary"
                    size="small"
                    icon={<PlusOutlined />}
                    className="ml-2"
                    onClick={() => {
                        const newGroup: FilterNode = {
                            id: nanoid(),
                            type: 'group',
                            logic: 'AND',
                            children: []
                        };
                        setFilterNodes(prev => [...prev, newGroup]);
                    }}
                >
                    添加组
                </Button>
            </div>

            {/* 渲染所有根节点 */}
            {filterNodes.map(node => renderNode(node))}
        </div>
    );
};

export default FilterOperation;
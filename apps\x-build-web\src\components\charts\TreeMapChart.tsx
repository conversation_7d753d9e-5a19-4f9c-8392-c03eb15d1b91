import type { TreemapChartProps } from '@web/src/types'
import { forwardRef } from 'react'
import { keepFixFloat, mapMetricToText, resolveField } from '@web/src/utils'
import EChart from './EChart'
import { isEmpty } from 'radash'

function TreeMapChart({ data, metrics, tags, tagDims, path, chartId, setPath, drillDown }: TreemapChartProps, ref: any) {
    if (metrics.length !== 1) {
        return <div>请选择一个指标</div>
    }
    if (!data.length || isEmpty(data[0])) {
        return <div>暂无数据</div>
    }
    const onclick = (params: any) => {
        chartId && drillDown && drillDown(chartId, params.name, tagDims)
        path && setPath && setPath([...path, params.name])
    }
    const options = {
        tooltip: {
            trigger: 'item',
            formatter: (params: any) => `${resolveField(params.name).tag}<br/>${params.marker} ${mapMetricToText(metrics[0])}：${keepFixFloat(params.value, 2, metrics[0])}`,
        },
        series: [
            {
                type: 'treemap',
                data: [{
                    value: data[0][metrics[0]]?.reduce((acc: number, cur: number) => acc + cur),
                    children: tags.map((tag, index) => ({
                        name: tag,
                        value: data[0][metrics[0]][index],
                    })),
                }],
                label: {
                    formatter: (params: any) => resolveField(params.name).tag,
                },
                breadcrumb: {
                    show: false,
                },
                animation: false,
            },
        ],
    }
    return (
        <EChart
            options={options}
            onClick={onclick}
            ref={ref}
        />
    )
}
export default forwardRef(TreeMapChart)

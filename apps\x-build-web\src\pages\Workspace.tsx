import { useState } from 'react';
import { Button, Input, Tabs, Card, Pagination, Avatar, Typography, Modal, Form, Upload, message, Flex, GetProp, UploadProps, UploadFile, Image, PaginationProps, TabsProps, Table } from 'antd';
import { SearchOutlined, PlusOutlined } from '@ant-design/icons';
import { useNavigate } from 'react-router';
// import { trpc } from '@web/src/service/tRPC';
// import { useMutation, useQuery } from '@tanstack/react-query';

const { Text, Title } = Typography;
const { TextArea } = Input;
type FileType = Parameters<GetProp<UploadProps, 'beforeUpload'>>[0];

const getBase64 = (file: FileType): Promise<string> =>
	new Promise((resolve, reject) => {
		const reader = new FileReader();
		reader.readAsDataURL(file);
		reader.onload = () => resolve(reader.result as string);
		reader.onerror = (error) => reject(error);
	});
interface WorkspaceItem {
	id: number;
	name: string;
	creator: string;
	lastModified: string;
	description: string;
	avatar?:string
}
const workspaceItems: WorkspaceItem[] = [
	{
		id: 1,
		name: '工作区名称1',
		creator: '张三',
		lastModified: '昨天',
		description: '111',
		avatar: '111'
	},
	{
		id: 2,
		name: '工作区名称2',
		creator: '张三',
		lastModified: '昨天',
		description: '111',
	}
];


const Workspace = () => {
	const [activeTab, setActiveTab] = useState('all');
	const [currentPage, setCurrentPage] = useState(1);
	const [pageSize, setPageSize] = useState(10);
	const [createOpen, setCreateOpen] = useState(false);
	const [copyOpen, setCopyOpen] = useState(false);
	const [previewOpen, setPreviewOpen] = useState(false);
	const [memberOpen, setMemberOpen] = useState(false);
	const [editOpen, setEditOpen] = useState(false);
	const [deleteOpen, setDeleteOpen] = useState(false);
	const [previewImage, setPreviewImage] = useState('');
	const [fileList, setFileList] = useState<UploadFile[]>([]);
	const [createForm] = Form.useForm();
	const [copyForm] = Form.useForm();
	const [editForm] = Form.useForm();
	const navigate = useNavigate();
	// const userQuery = useQuery(trpc.getUser.queryOptions({ id: 'id_bilbo' }));
	// const userCreator = useMutation(trpc.createUser.mutationOptions());
	// 处理标签页切换
	const handleTabChange: TabsProps['onChange'] = (key) => {
		setActiveTab(key);
	};

	// 处理分页变化
	const handlePageChange: PaginationProps['onChange'] = (page, pageSize) => {
		setCurrentPage(page);
		setPageSize(pageSize);
	};

	// 打开新建工作区对话框
	const showCreateModal = () => {
		setCreateOpen(true);
	};
	const handlePreview = async (file: UploadFile) => {
		if (!file.url && !file.preview) {
			file.preview = await getBase64(file.originFileObj as FileType);
		}
		setPreviewImage(file.url || (file.preview as string));
		setPreviewOpen(true);
	};
	const handleFileChange: UploadProps['onChange'] = ({ fileList: newFileList }) => {
		setFileList(newFileList);
	}
	// 处理新建确认
	const handleCreateOk = () => {
		createForm.validateFields()
			.then(values => {
				console.log('Form values:', values);
				// 这里可以添加创建工作区的逻辑
				message.success('工作区创建成功');
				createForm.resetFields();
				setCreateOpen(false);
			})
			.catch(info => {
				console.log('Validate Failed:', info);
			});
	};

	// 处理对话框取消
	const handleCreateCancel = () => {
		createForm.resetFields();
		setCreateOpen(false);
	};

	// 打开复制工作区对话框
	const showCopyModal = (item: WorkspaceItem) => {
		// 预填充表单数据
		copyForm.setFieldsValue({
			name: `${item.name} - 副本`,
			description: item.description,
		});
		setCopyOpen(true);
	};

	// 处理复制工作区对话框确认
	const handleCopyOk = () => {
		copyForm.validateFields()
			.then(values => {
				console.log('Copy Form values:', values);
				// 这里可以添加复制工作区的逻辑
				message.success('工作区复制成功');
				copyForm.resetFields();
				setCopyOpen(false);
			})
			.catch(info => {
				console.log('Copy Validate Failed:', info);
			});
	};
	const handleEditOk = () => {
		editForm.validateFields()
			.then(values => {
				console.log('Edit Form values:', values);
				// 这里可以添加复制工作区的逻辑
				message.success('工作区编辑成功');
				editForm.resetFields();
				setEditOpen(false);
			})
			.catch(info => {
				console.log('Edit Validate Failed:', info);
			});
	};
	// 处理复制工作区对话框取消
	const handleCopyCancel = () => {
		copyForm.resetFields();
		setCopyOpen(false);
	};
	const handleEditCancel = () => {
		editForm.resetFields();
		setEditOpen(false);
	};
	// 上传前检查文件
	const beforeUpload = (file: FileType) => {
		const isImage = file.type.startsWith('image/');
		if (!isImage) {
			message.error('只能上传图片文件!');
		}
		// const isLt2M = file.size / 1024 / 1024 < 2;
		// if (!isLt2M) {
		// 	message.error('图片必须小于2MB!');
		// }
		return isImage
		// && isLt2M;
	};

	// 工作区卡片渲染
	const renderWorkspaceCard = (item: WorkspaceItem) => (
		<Card
			key={item.id}
			className="hover:shadow-md transition-shadow duration-300"
			variant='borderless'
			hoverable
			actions={[
				<>
					<Button size="small" type='text' danger onClick={() => setDeleteOpen(true)}>删除</Button>
					<Button size="small" type='text' onClick={() => showCopyModal(item)}>复制工作区</Button>
					<Button size="small" type='text' onClick={() => setMemberOpen(true)}>成员管理</Button>
					<Button size="small" type='text' onClick={() => setEditOpen(true)}>编辑</Button>
				</>
			]}
		>
			<div className="flex mb-4">
				<div className="mr-4">
					<Avatar shape="square" size={64} className="bg-gray-100 text-gray-500">{item.avatar??'暂无图片'}</Avatar>
				</div>
				<div className="flex-1">
					<Flex vertical className="w-full items-start">
						<Title onClick={() => {
							navigate(`/workspace/${item.id}/dataset`)
						}} level={5}>{item.name}</Title>
						<Text type="secondary">创建人：{item.creator}</Text>
						<Text type="secondary">当前管理员：{item.lastModified}</Text>
						<Text type="secondary">描述：{item.description}</Text>
					</Flex>
				</div>
			</div>
		</Card>
	);

	return (
		<div className="p-6">
			<div className="flex justify-between mb-4">
				<Button type="primary" icon={<PlusOutlined />} className="bg-blue-500" onClick={showCreateModal}>
					新建工作区
				</Button>
				<Input
					placeholder="输入关键词搜索"
					prefix={<SearchOutlined />}
					className="w-72"
				/>
			</div>

			<Tabs
				activeKey={activeTab}
				onChange={handleTabChange}
				className="mb-4"
				items={[
					{
						key: 'all',
						label: '全部',
					},
					{
						key: 'created',
						label: '我创建的',
					},
					{
						key: 'invited',
						label: '邀请我的',
					},
				]}
			/>

			<div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-5 gap-4 mb-6">
				{workspaceItems.map(item => renderWorkspaceCard(item))}
			</div>

			<div className="text-center flex justify-end">
				<Pagination
					current={currentPage}
					pageSize={pageSize}
					total={workspaceItems.length}
					onChange={handlePageChange}
					showSizeChanger
					showQuickJumper
					showTotal={(total) => `共 ${total} 条`}
				/>
			</div>
			{/* 新建工作区对话框 */}
			<Modal
				title="新建工作区"
				open={createOpen}
				onOk={handleCreateOk}
				onCancel={handleCreateCancel}
				okText="确认"
				cancelText="取消"
			>
				<Form
					form={createForm}
					layout="vertical"
					name="create_workspace_form"
				>
					<Form.Item
						name="name"
						label="工作区名称"
						rules={[{ required: true, message: '请输入工作区名称' }]}
					>
						<Input placeholder="请输入工作区名称" />
					</Form.Item>
					<Form.Item
						name="description"
						label="描述"
					>
						<TextArea rows={4} placeholder="请输入工作区描述" />
					</Form.Item>
					<Form.Item
						name="image"
						label="工作区图片"
						valuePropName="fileList"
						getValueFromEvent={(e) => {
							if (Array.isArray(e)) {
								return e;
							}
							return e && e.fileList;
						}}
					>
						<Upload
							name="avatar"
							listType="picture"
							fileList={fileList}
							maxCount={1}
							showUploadList={false}
							beforeUpload={beforeUpload}
						>
							{previewImage ? null : <Button icon={<PlusOutlined />}>
								上传
							</Button>}
						</Upload>
					</Form.Item>
					{previewImage && (
						<Image
							wrapperStyle={{ display: 'none' }}
							preview={{
								visible: previewOpen,
								onVisibleChange: (visible) => setPreviewOpen(visible),
								afterOpenChange: (visible) => !visible && setPreviewImage(''),
							}}
							src={previewImage}
						/>
					)}
				</Form>
			</Modal>

			{/* 复制工作区对话框 */}
			<Modal
				title="复制工作区"
				open={copyOpen}
				onOk={handleCopyOk}
				onCancel={handleCopyCancel}
				okText="确认"
				cancelText="取消"
			>
				<Form
					form={copyForm}
					layout="vertical"
					name="copy_workspace_form"
				>
					<Form.Item
						name="name"
						label="工作区名称"
						rules={[{ required: true, message: '请输入工作区名称' }]}
					>
						<Input placeholder="请输入工作区名称" />
					</Form.Item>
					<Form.Item
						name="description"
						label="描述"
					>
						<TextArea rows={4} placeholder="请输入工作区描述" />
					</Form.Item>
					<Form.Item
						name="image"
						label="工作区图片"
						valuePropName="fileList"
						getValueFromEvent={(e) => {
							if (Array.isArray(e)) {
								return e;
							}
							return e && e.fileList;
						}}
					>
						<Upload
							name="avatar"
							listType="picture-card"
							maxCount={1}
							showUploadList={false}
							beforeUpload={beforeUpload}
							onPreview={handlePreview}
							onChange={handleFileChange}
						>
							{previewImage ? null : <Button icon={<PlusOutlined />}>
								上传
							</Button>}
						</Upload>
					</Form.Item>
					{previewImage && (
						<Image
							wrapperStyle={{ display: 'none' }}
							preview={{
								visible: previewOpen,
								onVisibleChange: (visible) => setPreviewOpen(visible),
								afterOpenChange: (visible) => !visible && setPreviewImage(''),
							}}
							src={previewImage}
						/>
					)}
				</Form>
			</Modal>
			<Modal
				title="编辑工作区"
				open={editOpen}
				onOk={handleEditOk}
				onCancel={handleEditCancel}
				okText="确认"
				cancelText="取消"
			>
				<Form
					form={editForm}
					layout="vertical"
					name="edit_workspace_form"
				>
					<Form.Item
						name="name"
						label="工作区名称"
						rules={[{ required: true, message: '请输入工作区名称' }]}
					>
						<Input placeholder="请输入工作区名称" />
					</Form.Item>
					<Form.Item
						name="description"
						label="描述"
					>
						<TextArea rows={4} placeholder="请输入工作区描述" />
					</Form.Item>
					<Form.Item
						name="image"
						label="工作区图片"
						valuePropName="fileList"
						getValueFromEvent={(e) => {
							if (Array.isArray(e)) {
								return e;
							}
							return e && e.fileList;
						}}
					>
						<Upload
							name="avatar"
							listType="picture-card"
							maxCount={1}
							showUploadList={false}
							beforeUpload={beforeUpload}
							onPreview={handlePreview}
							onChange={handleFileChange}
						>
							{previewImage ? null : <Button icon={<PlusOutlined />}>
								上传
							</Button>}
						</Upload>
					</Form.Item>
					{previewImage && (
						<Image
							wrapperStyle={{ display: 'none' }}
							preview={{
								visible: previewOpen,
								onVisibleChange: (visible) => setPreviewOpen(visible),
								afterOpenChange: (visible) => !visible && setPreviewImage(''),
							}}
							src={previewImage}
						/>
					)}
				</Form>
			</Modal>
			<Modal
				title="成员管理"
				open={memberOpen}
				onOk={() => setMemberOpen(false)}
				onCancel={() => setMemberOpen(false)}
				footer={null}
			>
				<Button type="link" icon={<PlusOutlined />}>添加成员</Button>
				<Table
					columns={[
						{
							title: '姓名',
							dataIndex: 'username',
							key: 'username',
						},
						{
							title: '权限',
							dataIndex: 'role',
							key: 'role',
						}
					]}
					dataSource={[
						{
							key: '1',
							username: '张三',
							role: '管理员',
						}
					]}
				/>
			</Modal>
			<Modal
				title="删除工作区"
				open={deleteOpen}
				onOk={() => setDeleteOpen(false)}
				onCancel={() => setDeleteOpen(false)}
				okText="确认"
				cancelText="取消"
			>
				<p>确认删除工作区吗？</p>
			</Modal>
		</div>
	);
};

export default Workspace;
// src/config/api-config.service.ts
import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as path from 'path';
import * as fs from 'fs';

@Injectable()
export class AppConfigService {
  constructor(private readonly configService: ConfigService) {}

  get databaseUrl(): string {
    return this.configService.get<string>('DATABASE_URL')!;
  }

  get fetchFusionUrl(): string {
    return this.configService.get<string>('FETCHFUSION_URL', 'localhost:9000');
  }

  get workDir(): string {
    return this.configService.get<string>('WORK_DIR', '/xbuild');
  }

  get S3Config() {
    return {
      region: this.configService.get<string>('S3_REGION')!,
      endpoint: this.configService.get<string>('S3_ENDPOINT')!,
      credentials: {
        accessKeyId: this.configService.get<string>('S3_ACCESS_KEY_ID')!,
        secretAccessKey: this.configService.get<string>('S3_SECRET_ACCESS_KEY')!,
      },
      forcePathStyle: true, // 必须设置为 true
    };
  }

  get S3DefaultBucket() {
    return this.configService.get<string>('S3_DEFAULT_BUCKET', 'xbuild');
  }

  public getFilePath(dir: string, fileName: string) {
    const dirPath = this.getOrCreateDirPath(dir);
    return path.posix.join(dirPath, fileName);
  }

  public getSampleDataFilePath(workspaceId: string, namespace: string) {
    return this.getFilePath(`workspace/${workspaceId}/sampledata/`, `${namespace}.db`);
  }

  public getTempFilePath(fileName: string) {
    return this.getFilePath('temp', fileName);
  }

  /* 
  获取目录实际路径，若不存在，则创建
  */
  private getOrCreateDirPath(dir: string) {
    const dirPath = path.resolve(this.workDir, dir);
    if (!fs.existsSync(dirPath)) {
      fs.mkdirSync(path.posix.join(this.workDir, dir), {
        recursive: true,
      })!;
    }
    return dirPath;
  }
}

import { EntityStructure } from './datasource.types';

/**
 * 生成 DuckDB 原始类型的映射
 */

export function mapPrimitiveType(type: string | undefined, format: string | undefined): string {
  if (!type) return 'VARCHAR';

  switch (type) {
    case 'integer':
      return format === 'int64' ? 'BIGINT' : 'INTEGER';

    case 'number':
      switch (format?.toLowerCase()) {
        case 'int32':
          return 'INTEGER';
        case 'int64':
          return 'BIGINT';
        case 'float':
          return 'FLOAT';
        default:
          return 'DOUBLE';
      }

    case 'string':
      switch (format?.toLowerCase()) {
        case 'date':
          return 'DATE';
        case 'date-time':
          return 'TIMESTAMPTZ';
        case 'binary':
          return 'BLOB';
        case 'password':
          return 'VARCHAR';
        case 'uuid':
          return 'UUID';
        default:
          return 'VARCHAR';
      }

    case 'boolean':
      return 'BOOLEAN';

    default:
      return 'VARCHAR';
  }
}

/**
 * 生成 DuckDB CREATE TABLE 语句
 */
export function generateDDL(tableName: string, structure: EntityStructure, schemaName?: string): string {
  if (structure.columns.length === 0) {
    throw new Error('Schema 中缺少 columns 定义');
  }
  const columns = [];
  // const columns: string[] = [];
  const safeTableName = /[ -()]/.test(tableName) ? `"${tableName}"` : tableName;
  // 生成列定义
  for (const column of structure.columns) {
    // 处理包含特殊字符的属性名
    const safeName = /[ -()]/.test(column.name) ? `"${column.name}"` : column.name;

    // 处理可为空约束
    // const required = structure.required || []; DEFAULT NULL
    const nullable = column.nullable ? '' : 'NOT NULL';

    columns.push(`${safeName} ${column.dataType} ${nullable}`.trim());
  }

  // 处理是否需要集成工作流能力，如果需要，为相关表填加_jobId字段
  // if (structure.workflowCapable) {
  //   columns.push(`${JobIdColumn} UUID`);
  // }

  // 处理主键约束
  if (structure.primaryKeys) {
    const formattedPKColumns = structure.primaryKeys.map((col) => (/[ -()]/.test(col) ? `"${col}"` : col)).join(', ');
    columns.push(`PRIMARY KEY (${formattedPKColumns})`);
  }

  // 生成最终的 DDL 语句
  if (schemaName) {
    return `CREATE OR REPLACE TABLE ${schemaName}.${safeTableName} (\n    ${columns.join(',\n    ')}\n);`;
  } else {
    return `CREATE OR REPLACE TABLE ${safeTableName} (\n    ${columns.join(',\n    ')}\n);`;
  }
}

// import {JobParameter} from '@server/common/constants';
// import { PropertyTypes } from '@server/common/utils';
// export function generateJobTableDDL(): string {
//   const t : PropertyTypes<JobParameter>
//     t.jobId
// }

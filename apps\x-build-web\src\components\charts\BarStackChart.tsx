import type { BarStackChartProps } from '@web/src/types'
import { keepFixFloat, mapMetricToText, resolveField } from '@web/src/utils'
import { forwardRef } from 'react'
import EChart from './EChart'
import { isEmpty } from 'radash'

function BarStackChart({
    data,
    tags,
    metrics,
    views,
    currentCategory,
}: BarStackChartProps, ref: any) {
    // console.log(metrics)
    // console.log(data)
    if (!data.length || isEmpty(data[0])) {
        return <div>暂无数据</div>
    }
    // const onClick = (params: any) => {
    //     if (crosses.find(cross => cross.charts.includes(chartId))) {
    //         drillSL(Object.fromEntries([
    //             // ...crosses.filter(cross => [11, 12, 13].filter(id => id !== chartId).includes(cross.id)).map(cross => [
    //             //     cross.metric,cross.tag
    //             // ]),
    //             ...crosses.filter(cross => [11, 12, 13].filter(id => id !== chartId).includes(cross.id)&&cross.charts.includes(chartId)).map(cross => [
    //                 cross.metric, cross.tag
    //             ]),
    //             [metrics[0], metrics[0] === 'sentiment' ? Number(params.seriesName) : params.seriesName],
    //             // [crosses.find(cross => cross.charts.includes(chartId)).metric, crosses.find(cross => cross.charts.includes(chartId)).tag],
    //         ]))
    //     }
    //     setCrosses(crosses.map(cross => cross.id === chartId ? { ...cross,metric:metrics[0], tag:metrics[0] === 'sentiment' ? Number(params.seriesName) : params.seriesName,charts: [11,12,13].filter(id=>id!==chartId) } : cross))
    // }
    const options = currentCategory.length === 1
        ? {
            tooltip: {
                trigger: 'item',
                formatter: (params: any) => `${resolveField(params.name).tag}<br/>${params.marker} ${mapMetricToText(metrics[0])}：${keepFixFloat(params.value, 2, metrics[0])}`,
            },
            // title: {
            //     text: name || '',
            // },
            legend: {
                textStyle: {
                    fontSize: '14px',
                },
                // top: 'center',
                // left: 'right',
                // orient: 'vertical',
                data: tags,
                formatter: (name: string) => resolveField(name).tag,
            },
            series: [
                {
                    type: 'pie',
                    data: data[0][metrics[0]]?.map((item: any, index: number) => ({ name: tags[index], value: item })),
                    radius: ['20%', '50%'],
                    avoidLabelOverlap: false,
                    label: {
                        show: false,
                        position: 'center',
                        formatter: (params: any) => resolveField(params.name).tag,
                    },
                    // emphasis: {
                    //     label: {
                    //         show: true,
                    //         // position:'center',
                    //         fontSize: 40,
                    //         fontWeight: 'bold',
                    //     }
                    // },
                    labelLine: {
                        show: false,
                    },
                },
            ],
        }
        : {
            tooltip: {
                trigger: 'axis',
                formatter: (params: any) => views.length > 1 ? undefined : `${resolveField(params.name).tag}<br/>${params.marker} ${mapMetricToText(metrics[0])}：${keepFixFloat(params.value, 2, metrics[0])}`,
            },
            // title: {
            //     text: name || '',
            // },
            grid: {
                left: '3%',
                right: '4%',
                bottom: '3%',
                containLabel: true,
            },
            legend: {
                textStyle: {
                    fontSize: '14px',
                },
                // top: 'center',
                // left: 'right',
                // orient: 'vertical',
                type: 'scroll',
            },
            xAxis: {
                type: 'category',
                axisLabel: {
                    interval: 0,
                    fontSize: '14px',
                    rotate: -40,
                },
                data: views.map(view => view.name),
            },
            yAxis: {
                type: 'value',
                axisLabel: {
                    fontSize: '14px',
                    formatter: (value: string) => keepFixFloat(value, 2, metrics[0]),
                },
            },
            series: tags.map((tag, index) => ({
                type: 'bar',
                // name: keywordSourceOptions.find(keyword => keyword.value === resolveField(tag).tag)?.label ?? resolveField(tag).tag,
                name: tag,
                stack: 'total',
                barMinHeight: 100,
                // label: {
                //     show: true,
                //     formatter: (params: any) => Math.round(params.value * 1000) / 10 + '%'
                // },
                data: views.map((_, index2) => data[index2][metrics[0]][index]),
            })),
        }
    if (metrics.length !== 1) {
        return <div>请选择一个指标</div>
    }
    if (!data.length) {
        return <div>暂无数据</div>
    }
    return (
        <EChart
            options={options}
            // onClick={onClick}
            ref={ref}
        />
    )
}
export default forwardRef(BarStackChart)

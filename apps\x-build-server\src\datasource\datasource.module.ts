import { HttpModule } from '@nestjs/axios';
import { Module } from '@nestjs/common';
import { AppConfigModule } from '@server/config/config.module';
import { PrismaService } from '@server/common/prisma.service';
import { DataSourceRouter } from './datasource.router';

@Module({
  imports: [AppConfigModule, HttpModule],
  providers: [DataSourceRouter, PrismaService],
  exports: [DataSourceRouter],
})
export class DataSourceModule {}

import type { MetricCardProps } from '@web/src/types'
// import { ThemeContext } from '@/store/ThemeContext'
import { ProCard, ProForm, ProFormCascader, ProFormDigit, ProFormList, ProFormText } from '@ant-design/pro-components'
import { Col, ConfigProvider, Form, Row, Splitter, Statistic, Tabs } from 'antd'
import { useEffect } from 'react'
import { keepFixFloat, resolveField } from '@web/src/utils'
import { isEmpty } from 'radash'

function MetricCard({ bizType, data, metrics, tags, period, cardData, setCardData }: MetricCardProps) {
    const [form] = Form.useForm()
    const values = Form.useWatch([], form)
    // const { themeValue } = useContext(ThemeContext)
    useEffect(() => {
        form.validateFields()
            .then(values => setCardData && values.attributes && setCardData(values.attributes))
            .catch(() => setCardData && setCardData([]))
    }, [values])
    if (period && period.length) {
        return <div>指标卡无日期选项</div>
    }
    if (!data.length || isEmpty(data[0])) {
        return <div>暂无数据</div>
    }
    if (bizType === 1) {
        return (
            <ProCard
                title={<div style={{ textAlign: 'center' }}>{tags[0]}</div>}
                headStyle={{
                    display: 'flex',
                    justifyContent: 'center',
                    fontSize: `${cardData.titleFontSize || 20}px`,
                    // color: themeValue.title?.textStyle?.color || '#666666',
                    // backgroundColor: themeValue.backgroundColor || '#fff',
                }}
                bodyStyle={{
                    fontSize: `${Number(cardData.titleFontSize) - 2 || 18}px`,
                    // color: themeValue.title?.textStyle?.color || '#9da7ac',
                    // backgroundColor: themeValue.backgroundColor || '#fff',
                }}
                layout="center"
            >
                <ConfigProvider
                    theme={{
                        components: {
                            Statistic: {
                                contentFontSize: Number(cardData.titleFontSize) - 2 || 18,
                            },
                        },
                    }}
                >
                    {
                        isEmpty(data[0])
                            ? <div>暂无数据</div>
                            : data[0][metrics[0]]
                    }
                </ConfigProvider>
            </ProCard>
        )
    }
    if (metrics.length < 4 || (metrics.length === 4 && metrics.includes('increase_rate'))) {
        return <div>请选择所有指标</div>
    }
    if (!setCardData) {
        return <ConfigProvider
            theme={{
                components: {
                    Splitter: {
                        splitTriggerSize: 0,
                        splitBarSize: 0,
                    },
                },
            }}
        >
            <Splitter layout="vertical">
                <Splitter.Panel resizable={false}>
                    {
                        cardData[0]
                        && (
                            <ProCard
                                title={(
                                    <div style={{
                                        fontSize: `${cardData[0].titleFontSize || 20}px`,
                                        textAlign: 'center',
                                        // color: themeValue.title?.textStyle?.color || '#666666',
                                    }}
                                    >
                                        {cardData[0].title}
                                    </div>
                                )}
                                bodyStyle={{
                                    // color: themeValue.title?.textStyle?.color || '#3fb1e3',
                                    // backgroundColor: themeValue.backgroundColor || '#fff',
                                }}
                                headStyle={{
                                    display: 'flex',
                                    justifyContent: 'center',
                                    // color: themeValue.title?.textStyle?.color || '#666666',
                                    // backgroundColor: themeValue.backgroundColor || '#fff',
                                }}
                                style={{
                                    // backgroundColor: themeValue.backgroundColor || '#fff',
                                    width: '100%',
                                    height: '100%',
                                }}
                                layout="center"
                                direction="column"
                            >
                                <ConfigProvider
                                    theme={{
                                        components: {
                                            Statistic: {
                                                contentFontSize: Number(cardData[0].titleFontSize) - 2 || 18,
                                            },
                                        },
                                    }}
                                >
                                    <Statistic
                                        value={cardData[0].selectedMetric.length > 0 && cardData[0].selectedMetric[1] === 'tag'
                                            ? resolveField(cardData[0].selectedMetric[0]).tag
                                            : tags.find(tag => tag === cardData[0].selectedMetric[0]) && data[0][cardData[0].selectedMetric[1]][tags.indexOf(cardData[0].selectedMetric[0])]
                                                ? keepFixFloat(data[0][cardData[0].selectedMetric[1]][tags.indexOf(cardData[0].selectedMetric[0])], 2, cardData[0].selectedMetric[1])
                                                : '暂无数据,请选择合适的日期'}
                                        valueStyle={{
                                            // color: themeValue.title?.textStyle?.color || '#3fb1e3',
                                            fontSize: `${Number(cardData[0].titleFontSize) - 2 || 18}px`,
                                            textAlign: 'center',
                                        }}
                                    />
                                </ConfigProvider>
                            </ProCard>
                        )
                    }
                </Splitter.Panel>
                {
                    cardData.length > 1
                    && (
                        <Splitter.Panel>
                            <Splitter>
                                {
                                    cardData.slice(1).map((card: any) => (
                                        <Splitter.Panel key={card.title}>
                                            <ProCard
                                                key={card.title}
                                                style={{
                                                    // backgroundColor: themeValue.backgroundColor || '#fff',
                                                    width: '100%',
                                                    height: '100%',
                                                }}
                                                title={(
                                                    <div style={{
                                                        fontSize: `${card.titleFontSize || 20}px`,
                                                        textAlign: 'center',
                                                        // color: themeValue.title?.textStyle?.color || '#666666',
                                                    }}
                                                    >
                                                        {card.title}
                                                    </div>
                                                )}
                                                headStyle={{
                                                    display: 'flex',
                                                    justifyContent: 'center',
                                                    // color: themeValue.title?.textStyle?.color || '#666666',
                                                }}
                                                bodyStyle={{
                                                    // color: themeValue.title?.textStyle?.color || '#6be6c1',
                                                }}
                                                layout="center"
                                                direction="column"
                                            >
                                                <ConfigProvider
                                                    theme={{
                                                        components: {
                                                            Statistic: {
                                                                contentFontSize: Number(card.titleFontSize) - 2 || 18,
                                                            },
                                                        },
                                                    }}
                                                >
                                                    <Statistic
                                                        value={card.selectedMetric.length > 0 && card.selectedMetric[1] === 'tag'
                                                            ? resolveField(card.selectedMetric[0]).tag
                                                            : tags.find(tag => tag === card.selectedMetric[0]) && data[0][card.selectedMetric[1]][tags.indexOf(card.selectedMetric[0])]
                                                                ? keepFixFloat(data[0][card.selectedMetric[1]][tags.indexOf(card.selectedMetric[0])], 2, card.selectedMetric[1])
                                                                : '暂无数据,请选择合适的日期'}
                                                        valueStyle={{
                                                            // color: themeValue.title?.textStyle?.color || '#3fb1e3',
                                                            fontSize: `${Number(card.titleFontSize) - 2 || 18}px`,
                                                            textAlign: 'center',
                                                        }}
                                                    />
                                                </ConfigProvider>
                                            </ProCard>
                                        </Splitter.Panel>
                                    ))
                                }
                            </Splitter>
                        </Splitter.Panel>
                    )
                }
            </Splitter>
        </ConfigProvider>
    }
    const cascaderOptions = tags.map(tag => ({
        value: tag,
        label: resolveField(tag).tag,
        children: ['tag', ...metrics].map(metric => ({
            value: metric,
            label: metric,
        })),
    }))
    const items = [
        {
            label: '编辑',
            key: '1',
            children: (
                <ProForm
                    layout="horizontal"
                    grid
                    submitter={{
                        render: () => [],
                    }}
                    form={form}
                >
                    <ProFormList
                        name="attributes"
                        creatorButtonProps={{
                            creatorButtonText: '添加指标卡',
                        }}
                        min={1}
                        max={4}
                        copyIconProps={false}
                        deleteIconProps={{ tooltipText: '删除' }}
                        itemRender={({ listDom, action }, { index }) => {
                            return (
                                <ProCard
                                    size="small"
                                    bordered={false}
                                    style={{ marginBlockEnd: 8 }}
                                    title={index === 0 ? `主指标卡` : `副指标卡${index}`}
                                    extra={action}
                                    bodyStyle={{ paddingBlockEnd: 0 }}
                                >
                                    {listDom}
                                </ProCard>
                            )
                        }}
                        creatorRecord={{ title: '', selectedMetric: [], titleFontSize: 20 }}
                        initialValue={
                            cardData || [{ title: '', selectedMetric: [], titleFontSize: 20 }]
                        }
                    >
                        <ProFormText
                            style={{ padding: 0 }}
                            width="md"
                            name="title"
                            label="标题"
                            placeholder="请输入标题"
                            rules={[{ required: true, message: '请输入标题' }]}
                        />
                        <ProFormDigit
                            width="md"
                            name="titleFontSize"
                            label="标题字号"
                            placeholder="请输入标题字号"
                            initialValue="20"
                        />
                        <ProForm.Item isListField style={{ marginBlockEnd: 0 }} label="维度/指标选择">
                            <ProFormCascader
                                name="selectedMetric"
                                fieldProps={{
                                    options: cascaderOptions,
                                    placeholder: '请选择维度或指标',
                                    allowClear: true,
                                    style: { width: '100%' },
                                }}
                            />
                        </ProForm.Item>
                    </ProFormList>
                </ProForm>
            ),
        },
        {
            label: '预览',
            key: '2',
            disabled: cardData.length === 0,
            children:
                cardData.length > 0
                && (
                    <>
                        <Row key={0}>
                            {
                                cardData[0]
                                && (
                                    <ProCard
                                        bodyStyle={{
                                            // color: themeValue.title?.textStyle?.color || '#3fb1e3',
                                            // backgroundColor: themeValue.backgroundColor || '#fff',
                                        }}
                                        title={(
                                            <div style={{
                                                fontSize: `${cardData[0].titleFontSize || 20}px`,
                                                textAlign: 'center',
                                                // color: themeValue.title?.textStyle?.color || '#666666',
                                            }}
                                            >
                                                {cardData[0].title}
                                            </div>
                                        )}
                                        headStyle={{
                                            display: 'flex',
                                            justifyContent: 'center',
                                            // color: themeValue.title?.textStyle?.color || '#666666',
                                            // backgroundColor: themeValue.backgroundColor || '#fff',
                                        }}
                                        style={{
                                            // backgroundColor: themeValue.backgroundColor || '#fff',
                                        }}
                                        layout="center"
                                        direction="column"
                                    >
                                        <ConfigProvider
                                            theme={{
                                                components: {
                                                    Statistic: {
                                                        contentFontSize: Number(cardData[0].titleFontSize) - 2 || 18,
                                                    },
                                                },
                                            }}
                                        >
                                            <Statistic
                                                value={cardData[0].selectedMetric.length > 0 && cardData[0].selectedMetric[1] === 'tag'
                                                    ? resolveField(cardData[0].selectedMetric[0]).tag
                                                    : tags.find(tag => tag === cardData[0].selectedMetric[0]) && data[0][cardData[0].selectedMetric[1]][tags.indexOf(cardData[0].selectedMetric[0])]
                                                        ? keepFixFloat(data[0][cardData[0].selectedMetric[1]][tags.indexOf(cardData[0].selectedMetric[0])], 2, cardData[0].selectedMetric[1])
                                                        : '暂无数据,请选择合适的日期'}
                                                valueStyle={{
                                                    // color: themeValue.title?.textStyle?.color || '#3fb1e3',
                                                    fontSize: `${Number(cardData[0].titleFontSize) - 2 || 18}px`,
                                                    textAlign: 'center',
                                                }}
                                            />
                                        </ConfigProvider>
                                    </ProCard>
                                )
                            }
                        </Row>
                        <Row key={1} gutter={16}>
                            {
                                cardData.length > 1 && cardData.slice(1).map((card: any) => (
                                    <Col key={card.title} span={24 / (cardData.length - 1)}>
                                        <ProCard
                                            key={card.title}
                                            title={(
                                                <div style={{
                                                    fontSize: `${card.titleFontSize || 20}px`,
                                                    textAlign: 'center',
                                                    // color: themeValue.title?.textStyle?.color || '#666666',
                                                }}
                                                >
                                                    {card.title}
                                                </div>
                                            )}
                                            style={{
                                                // backgroundColor: themeValue.backgroundColor || '#fff',
                                            }}
                                            headStyle={{
                                                display: 'flex',
                                                justifyContent: 'center',
                                                // color: themeValue.title?.textStyle?.color || '#666666',
                                            }}
                                            bodyStyle={{
                                                // color: themeValue.title?.textStyle?.color || '#6be6c1',
                                            }}
                                            layout="center"
                                            direction="column"
                                        >
                                            <ConfigProvider
                                                theme={{
                                                    components: {
                                                        Statistic: {
                                                            contentFontSize: Number(card.titleFontSize) - 2 || 18,
                                                        },
                                                    },
                                                }}
                                            >
                                                <Statistic
                                                    value={card.selectedMetric.length > 0 && card.selectedMetric[1] === 'tag'
                                                        ? resolveField(card.selectedMetric[0]).tag
                                                        : tags.find(tag => tag === card.selectedMetric[0]) && data[0][card.selectedMetric[1]][tags.indexOf(card.selectedMetric[0])]
                                                            ? keepFixFloat(data[0][card.selectedMetric[1]][tags.indexOf(card.selectedMetric[0])], 2, card.selectedMetric[1])
                                                            : '暂无数据,请选择合适的日期'}
                                                    valueStyle={{
                                                        // color: themeValue.title?.textStyle?.color || '#3fb1e3',
                                                        fontSize: `${Number(card.titleFontSize) - 2 || 18}px`,
                                                        textAlign: 'center',
                                                    }}
                                                />
                                            </ConfigProvider>
                                        </ProCard>
                                    </Col>
                                ))
                            }
                        </Row>
                    </>
                ),
        },
    ]
    return (
        <Tabs
            defaultActiveKey="1"
            centered
            items={items}
        />
    )
}
export default MetricCard

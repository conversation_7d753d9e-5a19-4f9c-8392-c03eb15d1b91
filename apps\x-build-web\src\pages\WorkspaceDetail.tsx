import { ProLayout, ProSettings } from "@ant-design/pro-components";
import { But<PERSON> } from "antd";
import { useMemo } from "react";
import { NavLink, Outlet, useLocation, useNavigate, useParams } from "react-router"

const settings: ProSettings | undefined = {
	fixSiderbar: true,
	layout: 'top',
	splitMenus: true,
	contentWidth: 'Fluid',
};
const hideMenuPaths = ['addView'];

const WorkspaceDetail = () => {
	const params = useParams()
	const location = useLocation()
	const navigate = useNavigate()
	// console.log(location.pathname.includes('addView'))
	const shouldHideMenu = useMemo(() => {
		return hideMenuPaths.some(key => location.pathname.includes(key));
	}, [location.pathname]);
	return (
		<ProLayout
			route={{
				path: `/workspace/${params.id}`,
				routes: [
					{
						path: '/dataset',
						name: '工作区数据集'
					},
					{
						path: '/operation',
						name: '操作面板'
					},
					{
						path: '/publishManage',
						name: '发布管理'
					}
				]
			}}
			headerRender={shouldHideMenu ? false : undefined}
			className="h-screen"
			token={{
				bgLayout: '#fff',
				header: {
					colorBgHeader: '#fff',
				},
				sider: {
					colorMenuBackground: '#fff',
				},
				pageContainer: {
					colorBgPageContainer: '#fff',
					paddingBlockPageContainerContent: 0,
					paddingInlinePageContainerContent: 0,
				},
			}}
			logo={null}
			menuItemRender={(item, dom) => (
				<NavLink
					className={({ isActive }) => isActive ? 'active' : ''}
					to={item.path?.substring(1) || 'dataset'}
				>
					{dom}
				</NavLink>
			)}
			pageTitleRender={false}
			headerTitleRender={() =>
				<Button type="primary" onClick={() => navigate('/workspace')}>
					返回
				</Button>
			}
			{...settings}
		>
			<Outlet />
		</ProLayout>
	)
}

export default WorkspaceDetail
/** @type {import('tailwindcss').Config} */
const plugin = require('tailwindcss/plugin')

export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {},
  },
plugins: [
  plugin(function ({matchVariant }) {
    matchVariant('rp', (value) => {
      // console.log('replace value ====', value); // 你可以打印一下看看编译的时候，收到的是什么值
      return `& ${value.replace(/_/g, ' ')}`;
    });
  })
]
}


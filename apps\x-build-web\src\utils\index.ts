import { MenuProps } from "antd";
import { CustomTreeNode, MetricsType, TgiTableData } from "@web/src/types";

// 递归查找并更新treeData节点
export const updateNodeByKey = (
  nodes: CustomTreeNode[],
  key: string,
  updater: (node: CustomTreeNode) => void
): CustomTreeNode[] => {
  return nodes.map((node) => {
    if (node.key === key) {
      const newNode = { ...node };
      updater(newNode);
      return newNode;
    }
    if (node.children) {
      return {
        ...node,
        children: updateNodeByKey(node.children, key, updater),
      };
    }
    return node;
  });
};

// 递归查找节点
export const findNodeByKey = (
  nodes: CustomTreeNode[],
  key: string
): CustomTreeNode | null => {
  for (const node of nodes) {
    if (node.key === key) return node;
    if (node.children) {
      const found = findNodeByKey(node.children, key);
      if (found) return found;
    }
  }
  return null;
};

export const tableMenu: MenuProps["items"] = [
  {
    label: "重命名",
    key: "rename",
  },
  {
    label: "删除",
    key: "delete",
  },
];

// export const getBreadcrumbItems = (routes: typeof config.routes, currentPath: string): ItemType[] => {
//   const pathSegments = currentPath.split('/').filter(Boolean);

//   function dfs(
//     routes: typeof config.routes,
//     segIdx: number,
//     accumulatedPath: string
//   ): ItemType[] | null {
//     if (segIdx >= pathSegments.length) return [];
//     const segment = pathSegments[segIdx];
//     let matchedRoute: (typeof config.routes)[number] | null = null;

//     // 优先静态段匹配
//     matchedRoute = routes.find(r => {
//       const segs = r.path.split('/').filter(Boolean);
//       return segs[segs.length - 1] === segment;
//     }) || null;
//     // 若无静态段，尝试动态段匹配
//     if (!matchedRoute) {
//       matchedRoute = routes.find(r => {
//         const segs = r.path.split('/').filter(Boolean);
//         const last = segs[segs.length - 1];
//         return last && last.startsWith(':');
//       }) || null;
//     }
//     if (!matchedRoute) return null;

//     const nextPath = accumulatedPath + '/' + segment;
//     const currentCrumb = { path: nextPath, title: matchedRoute.name };

//     if (matchedRoute.routes && matchedRoute.routes.length > 0 && segIdx + 1 < pathSegments.length) {
//       const childCrumbs = dfs(matchedRoute.routes, segIdx + 1, nextPath);
//       if (childCrumbs) {
//         return [currentCrumb, ...childCrumbs];
//       }
//     } else if (segIdx + 1 === pathSegments.length) {
//       // 已到最后一段
//       return [currentCrumb];
//     }
//     return null;
//   }

//   return dfs(routes, 0, '') || [];
// };
export function resolveField(field: string) {
  const match = /(\d+)_(.+)/.exec(field)
  if (match) {
    return { level: Number.parseInt(match[1]), tag: match[2] }
  }
  else {
    return { level: null, tag: field }
  }
}
export function ChangeDecimalToPercentage(data: any) {
  return `${(Math.abs(data) * 100).toFixed(2)}%`
}
export function keepFixFloat(value: any, dot: number, metric: any = '') {
  if (metric && ['increase_rate', 'rebase'].includes(metric))
    return ChangeDecimalToPercentage(value)
  if (metric && metric === 'tgi') {
    return Number.parseFloat(value).toFixed(dot)
  }
  return value
}
export function mapMetricToText(metric: MetricsType) {
  if (metric === 'tgi') {
    return 'tgi'
  }
  else if (metric === 'rebase' || metric === 'increase_rate') {
    return '占比'
  }
  else {
    return '计数'
  }
}
export function findTagByPartialTagInTrees(trees: any[], partialTag: string) {
  for (const tree of trees) {
    const fullTag = findTagByPartialTag(tree, partialTag)
    if (fullTag) {
      return fullTag
    }
  }
  return null // Return null if no matching tag is found in any tree
}
export function findTagByPartialTag(tree: any, partialTag: string): any {
  // Check if the current node's tag contains the partial tag
  if (tree.tag.includes(partialTag)) {
    return tree.tag
  }

  // Recursively search in children
  if (tree.children && tree.children.length > 0) {
    for (const child of tree.children) {
      const result = findTagByPartialTag(child, partialTag)
      if (result) {
        return result
      }
    }
  }

  return null
}
export function tgiSorter(a: TgiTableData, b: TgiTableData, path: any[]) {
  const valueA = getValueByPath(a, path)
  const valueB = getValueByPath(b, path)
  if (valueA === null && valueB === null)
    return 0
  if (valueA === null)
    return -1
  if (valueB === null)
    return 1
  return valueA - valueB
}
export function normalize(num: number, arr: any) {
  const max = Math.max(...arr)
  const min = Math.min(...arr)
  if (max === min) {
    return 1
  }
  // console.log((num - min) / (max - min));
  return (num - min) / (max - min)
}
export function getValueByPath(data: any, path: any[]) {
  if (!data || !path?.length)
    return null
  return path.reduce((acc, key) => {
    // 处理 acc 为 null 或 undefined 的情况
    if (acc == null)
      return null
    // 获取当前层级的值
    // 如果 key 是数字字符串，将其转换为数字以访问数组
    const index = !Number.isNaN(Number(key)) ? Number(key) : key
    const value = acc[index]
    // 如果值不存在，返回 null
    if (value === undefined)
      return null
    return value
  }, data)
}
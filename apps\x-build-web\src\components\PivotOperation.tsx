import { But<PERSON>, Select, Space } from "antd";
import { DeleteOutlined, SettingOutlined } from "@ant-design/icons";

type PivotOperationProps = {
  filters: { field: string; value: string | null }[];
  setFilters: React.Dispatch<React.SetStateAction<{ field: string; value: string | null }[]>>;
  pivotColumns: { field: string }[];
  setPivotColumns: React.Dispatch<React.SetStateAction<{ field: string }[]>>;
  pivotRows: { field: string; sort?: 'asc' | 'desc' | null }[];
  setPivotRows: React.Dispatch<React.SetStateAction<{ field: string; sort?: 'asc' | 'desc' | null }[]>>;
  pivotValues: {
    field: string;
    agg: 'sum' | 'count' | 'avg' | 'max' | 'min' | 'first' | 'last' | 'median' | 'join';
    joinSeparator?: string;
    sort?: 'asc' | 'desc' | null;
  }[];
  setPivotValues: React.Dispatch<React.SetStateAction<{
    field: string;
    agg: 'sum' | 'count' | 'avg' | 'max' | 'min' | 'first' | 'last' | 'median' | 'join';
    joinSeparator?: string;
    sort?: 'asc' | 'desc' | null;
  }[]>>;
  filterSelections: Record<string, string>;
  setFilterSelections: React.Dispatch<React.SetStateAction<Record<string, string>>>;
  getAvailableFields: (currentField: string, index: number, type: 'filters' | 'columns' | 'rows' | 'values') => string[];
  getEnumValues: (field: string) => any[];
  openSetting: (type: 'row' | 'value', idx: number) => void;
};

const PivotOperation = ({
  filters,
  setFilters,
  pivotColumns,
  setPivotColumns,
  pivotRows,
  setPivotRows,
  pivotValues,
  setPivotValues,
  filterSelections,
  setFilterSelections,
  getAvailableFields,
  getEnumValues,
  openSetting
}: PivotOperationProps) => {
  return (
    <>
      <div className="mb-4">
        <div className="flex flex-col gap-4 mb-2">
          {/* 筛选器 */}
          <Space>
            <div className="flex items-center mb-1">
              <div className="font-bold">筛选器</div>
              <Button
                size="small"
                className="ml-2"
                onClick={() => setFilters([...filters, { field: '', value: null }])}
              >+ 添加筛选器</Button>
            </div>
            <div className="flex flex-wrap gap-2 mb-1">
              {filters.map((item, idx) => (
                <div key={idx} className="flex items-center">
                  <Select
                    value={item.field || undefined}
                    onChange={value => {
                      const newFilters = [...filters];
                      newFilters[idx].field = value;
                      setFilters(newFilters);
                    }}
                    style={{ width: 120 }}
                    placeholder="选择字段"
                    options={getAvailableFields(item.field, idx, 'filters').map(key => ({ label: key, value: key }))}
                    allowClear
                  />
                  <Button
                    type="text"
                    size="small"
                    icon={<DeleteOutlined />}
                    onClick={() => setFilters(filters.filter((_, i) => i !== idx))}
                  />
                </div>
              ))}
            </div>
          </Space>
          {/* 列 */}
          <Space>
            <div className="flex items-center mb-1">
              <div className="font-bold">列</div>
              <Button
                size="small"
                className="ml-2"
                onClick={() => setPivotColumns([...pivotColumns, { field: '' }])}
              >+ 添加列</Button>
            </div>
            <div className="flex flex-wrap gap-2 mb-1">
              {pivotColumns.map((item, idx) => (
                <div key={idx} className="flex items-center">
                  <Select
                    value={item.field || undefined}
                    onChange={value => {
                      const newCols = [...pivotColumns];
                      newCols[idx].field = value;
                      setPivotColumns(newCols);
                    }}
                    style={{ width: 120 }}
                    placeholder="选择字段"
                    options={getAvailableFields(item.field, idx, 'columns').map(key => ({ label: key, value: key }))}
                    allowClear
                  />
                  <Button
                    type="text"
                    size="small"
                    icon={<DeleteOutlined />}
                    onClick={() => setPivotColumns(pivotColumns.filter((_, i) => i !== idx))}
                  />
                </div>
              ))}
            </div>
          </Space>
          {/* 行 */}
          <Space>
            <div className="flex items-center mb-1">
              <div className="font-bold">行</div>
              <Button
                size="small"
                className="ml-2"
                onClick={() => setPivotRows([...pivotRows, { field: '' }])}
              >+ 添加行</Button>
            </div>
            <div className="flex flex-wrap gap-2 mb-1">
              {pivotRows.map((item, idx) => (
                <div key={idx} className="flex items-center">
                  <Select
                    value={item.field || undefined}
                    onChange={value => {
                      const newRows = [...pivotRows];
                      newRows[idx].field = value;
                      setPivotRows(newRows);
                    }}
                    style={{ width: 120 }}
                    placeholder="选择字段"
                    options={getAvailableFields(item.field, idx, 'rows').map(key => ({ label: key, value: key }))}
                    allowClear
                  />
                  <Button
                    type="text"
                    size="small"
                    icon={<SettingOutlined />}
                    onClick={() => openSetting('row', idx)}
                  />
                </div>
              ))}
            </div>
          </Space>
          {/* 值 */}
          <Space>
            <div className="flex items-center mb-1">
              <div className="font-bold">值</div>
              <Button
                size="small"
                className="ml-2"
                onClick={() => setPivotValues([...pivotValues, { field: '', agg: 'sum' }])}
              >+ 添加值</Button>
            </div>
            <div className="flex flex-wrap gap-2 mb-1">
              {pivotValues.map((item, idx) => (
                <div key={idx} className="flex items-center">
                  <Select
                    value={item.field || undefined}
                    onChange={value => {
                      const newVals = [...pivotValues];
                      newVals[idx].field = value;
                      setPivotValues(newVals);
                    }}
                    style={{ width: 120 }}
                    placeholder="选择字段"
                    options={getAvailableFields(item.field, idx, 'values').map(key => ({ label: key, value: key }))}
                    allowClear
                  />
                  <Button
                    type="text"
                    size="small"
                    icon={<SettingOutlined />}
                    onClick={() => openSetting('value', idx)}
                  />
                </div>
              ))}
            </div>
          </Space>
        </div>
      </div>
      {/* 筛选器字段渲染 */}
      {filters.filter(f => f.field).map(f => (
        <div key={f.field} className="inline-block mr-4 mb-2">
          <span className="mr-1">{f.field}：</span>
          <Select
            value={filterSelections[f.field] || '全部'}
            style={{ width: 120 }}
            onChange={val => setFilterSelections(s => ({ ...s, [f.field]: val }))}
            options={[
              { label: '全部', value: '全部' },
              ...getEnumValues(f.field).map(v => ({ label: String(v), value: String(v) }))
            ]}
          />
        </div>
      ))}
    </>
  );
};

export default PivotOperation;
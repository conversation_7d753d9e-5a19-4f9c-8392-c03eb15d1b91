import { Handle, Position } from "@xyflow/react";

// 自定义节点组件
const CustomNode = ({ data }: { data: { label: string, fields?: string[] } }) => {
	return (
		<div className="bg-white border border-gray-300 rounded shadow-sm p-2 w-full">
			{/* <Handle /> */}
			<div className="bg-blue-500 text-white p-2 -m-2 mb-2 rounded-t font-medium text-center">
				{data.label}
			</div>
			<Handle type="source" position={Position.Left} id="left" />
			<Handle type="target" position={Position.Right} id="right" />
			{data.fields && data.fields.map((field, index) => (
				<div key={index} className="py-1 px-2 border-t border-gray-200 text-sm">
					{field}
				</div>
			))}
		</div>
	);
};
export default CustomNode;
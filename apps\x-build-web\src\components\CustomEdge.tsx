import { BaseEdge, Edge, EdgeLabelRenderer, EdgeProps, getBezierPath } from "@xyflow/react";
import EdgeLabel from "./EdgeLabel";
import { FC } from "react";

const CustomEdge: FC<
	EdgeProps<Edge<{ startLabel: string; endLabel: string }>>
> = ({
	id,
	sourceX,
	sourceY,
	targetX,
	targetY,
	sourcePosition,
	targetPosition,
	// style = {},
	data,
	// markerEnd,
}) => {
		const [edgePath] = getBezierPath({
			sourceX,
			sourceY,
			sourcePosition,
			targetX,
			targetY,
			targetPosition,
		});
		// const [edgePath, labelX, labelY] = getStraightPath({
		// 	sourceX,
		// 	sourceY,
		// 	targetX,
		// 	targetY,
		// });
		// const { setEdges } = useReactFlow();
		return (
			<>
				<BaseEdge
					id={id}
					path={edgePath}
				/>
				<EdgeLabelRenderer>
					{/* <button
					style={{
						position: 'absolute',
						transform: `translate(-50%, -50%) translate(${labelX}px, ${labelY}px)`,
						pointerEvents: 'all',
					}}
					onClick={() => setEdges((edges) => edges.filter((e) => e.id !== id))}
				>
					{data.label}
				</button> */}
					{data?.startLabel && (
						<EdgeLabel
							transform={`translate(-50%, 0%) translate(${sourceX}px,${sourceY}px)`}
							label={data.startLabel}
						/>
					)}
					{data?.endLabel && (
						<EdgeLabel
							transform={`translate(-50%, -100%) translate(${targetX}px,${targetY}px)`}
							label={data.endLabel}
						/>
					)}
				</EdgeLabelRenderer>
			</>
		);
	};
export default CustomEdge;
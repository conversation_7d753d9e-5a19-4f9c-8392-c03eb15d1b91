import { Command, CommandRunner, Option } from 'nest-commander';
import { Logger } from '@nestjs/common';
import { ExternalApiService } from '@server/datasource/external-api/external-api.service';
// import { PrismaClient } from '@prisma/client';
import { PublicWorkspaceId } from '@server/common/constants';
import { PrismaService } from '@server/common/prisma.service';

interface CommandOptions {
  seed?: boolean;
}

@Command({
  name: 'init',
  description: '初始化应用数据',
})
export class AppInitCommand extends CommandRunner {
  private readonly logger = new Logger(AppInitCommand.name);

  constructor(
    private readonly apiService: ExternalApiService,
    private readonly prisma: PrismaService,
  ) {
    super();
  }

  async run(inputs: string[], options: CommandOptions): Promise<void> {
    this.logger.log('开始执行初始化命令...');
    try {
      console.log(options);

      this.logger.log('开始初始化数据库...');
      // 播种测试数据的逻辑
      // const prisma = new PrismaClient();

      this.logger.log('创建公共工作区...');
      await this.prisma.workspace.upsert({
        where: {
          id: PublicWorkspaceId,
        },
        update: {},
        create: {
          id: PublicWorkspaceId,
          name: 'public',
          description: '公共工作区',
        },
      });
      // }

      await this.apiService.syncFromFetchFusion();

      this.logger.log('初始化命令执行完成');
    } catch (error) {
      this.logger.error('初始化失败', error);
      process.exit(1);
    }
  }

  @Option({
    flags: '-s, --seed',
    description: '同时播种测试数据',
  })
  parseSeed(val: string): string {
    console.log(val);
    return val;
  }
}

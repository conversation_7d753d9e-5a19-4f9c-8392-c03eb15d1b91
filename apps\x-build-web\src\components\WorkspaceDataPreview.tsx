import { DownloadOutlined } from "@ant-design/icons";
import { Button, Table, Typography, Spin, Alert, Empty } from "antd";
import { useState, useMemo } from "react";
import { EntityStructure } from "../types";
import { useQuery } from "@tanstack/react-query";
import { trpc } from "../utils/tRPC";
const { Title } = Typography;

interface DataPreviewProps {
	entityId?: string;
}
// 动态生成表结构列定义
const structureColumns = [
	{
		title: '序号',
		key: 'index',
		render: (_: any, __: any, index: number) => index + 1,
	},
	{
		title: '字段名',
		dataIndex: 'name',
		key: 'name',
	},
	{
		title: '字段类型',
		dataIndex: 'dataType',
		key: 'dataType',
	},
	{
		title: '可为空',
		dataIndex: 'nullable',
		key: 'nullable',
		render: (nullable: boolean) => nullable ? '是' : '否',
	},
	{
		title: '字段说明',
		dataIndex: 'description',
		key: 'description',
		render: (description?: string) => description || '-',
	},
];

const WorkspaceDataPreview = ({ entityId }: DataPreviewProps) => {
	// 如果没有选中实体，显示空状态
	if (!entityId) {
		return (
			<div className="bg-white shadow-sm flex flex-1 items-center justify-center">
				<Empty description="请选择一个表来预览数据" />
			</div>
		);
	}
	const [currentPage, setCurrentPage] = useState(1);
	const [pageSize, setPageSize] = useState(10);

	// 获取表结构数据
	const { data: entityStructure, isLoading: structureLoading, error: structureError } = useQuery({
		...trpc.dataSource.getEntityStructure.queryOptions({ entityId: entityId! }),
		enabled: !!entityId,
	});

	// 获取示例数据
	const { data: sampleDataResponse, isLoading: dataLoading, error: dataError } = useQuery({
		...trpc.dataSource.querySampleData.queryOptions({
			entityId: entityId!,
			page: currentPage,
			pageSize
		}),
		enabled: !!entityId,
	});

	// 处理数据 - entityStructure 是 JSON 对象，需要转换为 EntityStructure 类型
	const structureData = entityStructure as EntityStructure | undefined;
	const sampleData = sampleDataResponse?.records || [];
	const totalCount = sampleDataResponse?.totalCount || 0;
	// 动态生成数据列定义
	const dataColumns = useMemo(() => {
		if (!structureData?.columns || structureData.columns.length === 0) {
			return [];
		}

		return structureData.columns.map((column) => ({
			title: column.name,
			dataIndex: column.name,
			key: column.name,
			ellipsis: true,
			render: (value: any) => {
				if (value === null || value === undefined) {
					return '-';
				}
				let displayValue: string;
				if (typeof value === 'object') {
					displayValue = JSON.stringify(value);
				} else {
					displayValue = String(value);
				}

				return (
					<span title={displayValue}>
						{displayValue}
					</span>
				);
			},
		}));
	}, [structureData]);
	const handlePageChange = (page: number, pageSize?: number) => {
		setCurrentPage(page);
		if (pageSize) setPageSize(pageSize);
	};


	// 加载状态
	if (structureLoading || dataLoading) {
		return (
			<div className="bg-white shadow-sm flex flex-1 items-center justify-center">
				<Spin size="large" />
			</div>
		);
	}

	// 错误状态
	if (structureError || dataError) {
		return (
			<div className="bg-white shadow-sm flex flex-1 items-center justify-center">
				<Alert
					message="加载失败"
					description={structureError?.message || dataError?.message || "未知错误"}
					type="error"
					showIcon
				/>
			</div>
		);
	}


	return (
		<div className="bg-white shadow-sm grid grid-cols-[1fr_auto_2fr] gap-2">
			{/* 左侧区域 - 表结构 (1/3 宽度) */}
			<div className="min-w-0">
				<div className="flex justify-between items-center mb-2">
					<Title level={5} className="m-0">表结构</Title>
					<Button type="primary" size="small">编辑</Button>
				</div>
				<div className="overflow-hidden">
					<Table
						columns={structureColumns}
						dataSource={structureData?.columns || []}
						pagination={{
							pageSize,
							current: currentPage,
							total: totalCount,
							onChange: handlePageChange,
							showSizeChanger: true,
							showQuickJumper: true,
							showTotal: (total) => `共 ${total} 条`,
						}}
						bordered
						// className="mb-2"
						scroll={{ x: 'max-content', y: 600 }}
						// size="small"
					/>
				</div>
			</div>
			{/* 中间分割线 */}
			<div className="border-l border-gray-200" />
			{/* 右侧区域 - 数据表格 (2/3 宽度) */}
			<div className="min-w-0">
				<div className="flex justify-end items-center mb-2">
					<Button type="link" icon={<DownloadOutlined />} />
				</div>
				<div className="overflow-hidden">
					<Table
						columns={dataColumns}
						dataSource={sampleData}
						pagination={{
							pageSize,
							current: currentPage,
							total: totalCount,
							onChange: handlePageChange,
							showSizeChanger: true,
							showQuickJumper: true,
							// pageSizeOptions:Array.from({ length: 100 }, (_, i) => (i + 1)),
							showTotal: (total) => `共 ${total} 条`,
						}}
						scroll={{ x: 'max-content', y: 600 }}
						bordered
						// className="mb-4"
						// size="small"
					/>
				</div>
			</div>
		</div>
	)
}
export default WorkspaceDataPreview;
import { Injectable, NestMiddleware } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';
import Keycloak from 'keycloak-connect';

@Injectable()
export class SSOMiddleware implements NestMiddleware {
  private keycloak = new Keycloak();

  constructor() {
    // 初始化 Keycloak 配置
    const keycloakConfig = {
      realm: 'your-realm',
      'auth-server-url': 'http://localhost:8080/auth',
      'ssl-required': 'external',
      resource: 'your-client-id',
      'bearer-only': true,
      'confidential-port': 0,
    };

    this.keycloak = new Keycloak({}, keycloakConfig);
  }

  async use(req: Request, res: Response, next: NextFunction) {
    // 使用 Keycloak 保护所有路由
    await this.keycloak.protect()(req, res, next);
  }
}

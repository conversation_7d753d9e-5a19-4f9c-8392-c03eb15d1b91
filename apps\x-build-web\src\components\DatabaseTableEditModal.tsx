import { Modal, Form, Input, Select, Button, Space } from 'antd';
import { DatabaseInfo } from '../types';

interface DatabaseTableEditModalProps {
	open: boolean;
	initialValues: DatabaseInfo;
	onOk: () => void;
	onCancel: () => void;
	onTestConnection?: (values: DatabaseInfo) => void;
	confirmLoading?: boolean;
}

const dbTypes = [
	{ label: 'MySQL', value: 'mysql' },
	{ label: 'PostgreSQL', value: 'postgres' },
	{ label: 'SQL Server', value: 'sqlserver' },
	{ label: 'Oracle', value: 'oracle' },
];

const DatabaseTableEditModal: React.FC<DatabaseTableEditModalProps> = ({ open, initialValues, onOk, onCancel, onTestConnection, confirmLoading }) => {
	const [form] = Form.useForm<DatabaseInfo>();
	const handleTest = async () => {
		const values = await form.validateFields();
		onTestConnection?.(values);
	};

	return (
		<Modal
			open={open}
			title={<span style={{ fontWeight: 700, fontSize: 24 }}>编辑数据库表</span>}
			onCancel={onCancel}
			footer={
				<Space>
					<Button onClick={onCancel}>取消</Button>
					<Button onClick={handleTest}>测试连接</Button>
					<Button type="primary" htmlType="submit" loading={confirmLoading}>确定</Button>
				</Space>
			}
			// destroyOnHidden
			width={700}
		>
			<Form
				form={form}
				layout="vertical"
				onFinish={onOk}
				initialValues={initialValues}
			>
				<div className="grid grid-cols-2 gap-x-8 gap-y-2">
					<Form.Item name="name" label="名称" rules={[{ required: true, message: '请输入名称' }]}>
						<Input placeholder="请输入名称" />
					</Form.Item>
					<Form.Item name="type" label="类型" rules={[{ required: true, message: '请选择类型' }]}>
						<Select placeholder="请选择" options={dbTypes} />
					</Form.Item>
					<Form.Item name="host" label="主机" rules={[{ required: true, message: '请输入主机' }]}>
						<Input placeholder="请输入主机" />
					</Form.Item>
					<Form.Item name="port" label="端口" rules={[{ required: true, message: '请输入端口号' }]}>
						<Input placeholder="请输入端口号" />
					</Form.Item>
					<Form.Item name="username" label="用户名" rules={[{ required: true, message: '请输入用户名' }]}>
						<Input placeholder="请输入用户名" />
					</Form.Item>
					<Form.Item name="password" label="密码" rules={[{ required: true, message: '请输入密码' }]}>
						<Input.Password placeholder="请输入密码" />
					</Form.Item>
				</div>
			</Form>
		</Modal>
	);
};

export default DatabaseTableEditModal; 
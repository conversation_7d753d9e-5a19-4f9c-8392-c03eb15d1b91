import { Modu<PERSON> } from '@nestjs/common';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { TRPCModule } from 'nestjs-trpc';
import { ConfigModule } from '@nestjs/config';
import configuration from './config/configuration';
import { AppInitCommand } from './command/app-init.command';
import { PrismaService } from './common/prisma.service';
import { ExternalApiModule } from './datasource/external-api/external-api.module';
import { AppConfigModule } from './config/config.module';
import { DataSourceModule } from './datasource/datasource.module';
import { FileModule } from './datasource/file/file.module';
@Module({
  imports: [
    TRPCModule.forRoot({
      autoSchemaFile: './src/@generated',
    }),
    ConfigModule.forRoot({
      load: [configuration],
      isGlobal: true,
    }),
    AppConfigModule,
    ExternalApiModule,
    DataSourceModule,
    FileModule,
  ],
  controllers: [AppController],
  providers: [AppService, AppInitCommand, PrismaService],
})
export class AppModule {}
